@echo off
cd /d "%~dp0"

:MENU
cls
echo ====================================================
echo                  MONTECHPOS TOOL v3.1
echo ====================================================
echo 1. Cai dat thu vien Python
echo 2. MySQL Database: tao moi, export, import, xoa...
echo 3. Cap quyen cho user 'montechpos' tren 1 database
echo 4. Redis: cai dat, kiem tra redis...
echo 5. Nginx: dich vu domain...
echo 6. Mo MySQL client (dang nhap vao mysql^>)
echo 7. Huong dan
echo 0. Thoat
echo ====================================================
set /p CHOICE=Nhap lua chon (1-7): 

if "%CHOICE%"=="1" goto PYLIB
if "%CHOICE%"=="2" goto MYSQLTOOL
if "%CHOICE%"=="3" goto GRANT
if "%CHOICE%"=="4" goto REDIS
if "%CHOICE%"=="5" goto NGINX
if "%CHOICE%"=="6" goto MYSQLCLIENT
if "%CHOICE%"=="7" goto HDSD
if "%CHOICE%"=="0" exit

echo Lua chon khong hop le.
pause
goto MENU

:PYLIB
echo ====================================================
echo     CAI DAT THU VIEN PYTHON (KHONG DUNG VENV)
echo ====================================================
echo [1/2] Cap nhat pip (neu can)...
python -m pip install --upgrade pip

echo [2/2] Cai dat cac goi trong requirements.txt...
python -m pip install -r requirements.txt

echo.
echo ----- HOAN TAT! Da cai dat xong thu vien toan cuc.
pause
goto MENU


:MYSQLTOOL
REM Chay cong cu quan ly MySQL
python mysql_tool.py
REM Dung man hinh de xem log neu co loi
pause
goto MENU


:GRANT
echo ====================================================
echo          CAP QUYEN USER TRUY CAP DATABASE 
echo ====================================================
set /p DBNAME=Nhap ten database can cap quyen (vd: logs_cxauco): 
echo.
set /p CONFIRM=Ban chac chan muon cap quyen cho 'montechpos' tren DB "%DBNAME%"? (yes/no): 

if /I "%CONFIRM%"=="yes" (
    echo.
    echo === Buoc 1: Tao user neu chua co ===
    "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p -e ^
    "CREATE USER IF NOT EXISTS 'montechpos'@'localhost' IDENTIFIED BY '77997799';"

    echo.
    echo === Buoc 2: Cap quyen tren DB %DBNAME% ===
    "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p -e ^
    "GRANT ALL PRIVILEGES ON %DBNAME%.* TO 'montechpos'@'localhost'; FLUSH PRIVILEGES;"

    echo.
    echo Da cap quyen thanh cong cho 'montechpos' tren database: %DBNAME%
) else (
    echo Da huy thao tac. Quay lai menu...
)

pause
goto MENU

:REDIS
setlocal enabledelayedexpansion

set "REDIS_PATH=C:\Redis-x64-3.0.504"
set "SERVICE_NAME=Redis"

echo ====================================================
echo                     REDIS SERVICE
echo ====================================================
REM === Kiem tra da cai Redis service chua ===
sc query "%SERVICE_NAME%" >nul 2>&1
if %ERRORLEVEL%==0 (
    echo Redis service da duoc cai san "C:\Redis-x64-3.0.504"
) else (
    echo Chua cai Redis service – Dang cai dat...
    cd /d "%REDIS_PATH%"
    redis-server.exe --service-install redis.windows.conf --loglevel verbose
    if %ERRORLEVEL%==0 (
        echo Cai dat service thanh cong!
    ) else (
        echo Loi khi cai dat Redis. Kiem tra file redis.windows.conf
        pause
        exit /b
    )
)

REM === Kiem tra Redis co dang chay hay khong ===
sc query "%SERVICE_NAME%" | find /I "RUNNING" >nul
if %ERRORLEVEL%==0 (
    echo Redis dang chay.
) else (
    echo Redis chua chay – Dang bat Redis...
    net start "%SERVICE_NAME%"
    if %ERRORLEVEL%==0 (
        echo Redis da duoc bat thanh cong.
    ) else (
        echo Khong the bat Redis. Kiem tra service hoac quyen Admin.
    )
)

pause
goto MENU


:NGINX
echo Da chuyen qua 3_nssm_service_nginx.bat
pause
goto MENU

:MYSQLCLIENT
echo ====================================================
echo              CHAY CAC LENH MYSQL CLIENT
echo ====================================================
echo Dang mo MySQL client, vui long nhap mat khau...
echo Hay chon db truoc khi truy van: use logs_tendb
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p
goto MENU

:HDSD
echo ====================================================
echo               HUONG DAN CAI DAT BAN DAU 
echo ====================================================
echo Buoc 1: Chay menu 1 de cai thu vien.
echo Buoc 2: Chay menu 2 de tao bang database moi.
echo Buoc 3: Chay menu 3 cap quyen user truy cap database.
echo Buoc 4: Chay menu 4 de kiem tra Redis co dang chay de
echo         dem so luong nguoi dang truy cap. 
echo Buoc 5: Chay menu 5 neu co nhu cau su dung Nginx de
echo         nhieu Flask app chay cung 1 vps (nhieu port). 
echo Xong.
echo ====================================================
pause
goto MENU