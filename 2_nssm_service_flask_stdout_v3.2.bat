@echo off 
title QUAN LY NSSM MontechPos (KHONG DUNG VENV)
setlocal enabledelayedexpansion

rem Cau hinh duong dan goc chua cac thu muc tram
set ROOT_PATH=C:\Users\<USER>\Documents

rem Dung dung python he thong (khong dung venv)
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe

rem Cau hinh duong dan NSSM
set NSSM_PATH=C:\nssm-2.24\win64\nssm.exe

rem Quet danh sach cac thu muc bat dau bang "montechpos_"

:chon_du_an
cls
echo ====================================
echo      DANH SACH THU MUC DU AN
echo ====================================
set i=0

for /d %%D in (%ROOT_PATH%\montechpos_*) do (
    set /a i+=1
    set "DIR_!i!=%%D"
    echo !i!. %%D
)

echo.
set /p CHOICE=Nhap so thu tu thu muc (hoac 0 de thoat): 
if "%CHOICE%"=="0" exit

if not defined DIR_%CHOICE% (
    echo Lua chon khong hop le.
    pause
    exit
)

set "BASE_PATH=!DIR_%CHOICE%!"
for %%A in ("!BASE_PATH!") do set "FOLDER_NAME=%%~nxA"
set "SERVICE_FLASK=FlaskApp_!FOLDER_NAME!"
set "SERVICE_SCHED=Scheduler_!FOLDER_NAME!"
set "LOG_PATH=!BASE_PATH!\debug"

goto menu

:menu
cls
echo ====================================
echo     QUAN LY NSSM MONTECHPOS v3.1
echo ====================================
echo Thu muc du an: %BASE_PATH%
echo Python.exe   : %PYTHON_PATH%
echo.
echo 1. Kiem tra trang thai dich vu
echo 2. Tam dung dich vu
echo 3. Dang ky dich vu moi
echo 4. Chay dich vu
echo 5. Huy bo dich vu da tao
echo 6. Khoi dong lai dich vu
echo 9. Xoa tat ca dich vu tren may
echo 0. Quay lai danh sach du an
echo.
set /p choice=Nhap lua chon (0-6): 

if "%choice%"=="1" goto check
if "%choice%"=="2" goto stop
if "%choice%"=="3" goto register
if "%choice%"=="4" goto start
if "%choice%"=="5" goto remove
if "%choice%"=="6" goto restart
if "%choice%"=="9" goto remove_all_services
if "%choice%"=="0" goto chon_du_an

goto menu

:check
cls
sc query "%SERVICE_FLASK%"
sc query "%SERVICE_SCHED%"
pause
goto menu

:stop
cls
net stop "%SERVICE_FLASK%"
net stop "%SERVICE_SCHED%"
pause
goto menu

:register
cls
echo Dang ky dich vu...

rem Tao thu muc log va log_std neu chua ton tai
if not exist "%LOG_PATH%" (
    mkdir "%LOG_PATH%"
)
if not exist "%LOG_PATH%\logs_std" (
    mkdir "%LOG_PATH%\logs_std"
)

rem ==== FlaskApp ====
%NSSM_PATH% install "%SERVICE_FLASK%" "%PYTHON_PATH%" "%BASE_PATH%\app.py"
%NSSM_PATH% set "%SERVICE_FLASK%" AppDirectory "%BASE_PATH%"
%NSSM_PATH% set "%SERVICE_FLASK%" AppExit Default Restart
%NSSM_PATH% set "%SERVICE_FLASK%" Start SERVICE_AUTO_START
%NSSM_PATH% set "%SERVICE_FLASK%" AppStdout "%LOG_PATH%\logs_std\app_stdout.log"
%NSSM_PATH% set "%SERVICE_FLASK%" AppStderr "%LOG_PATH%\logs_std\app_stderr.log"
%NSSM_PATH% set "%SERVICE_FLASK%" AppRotateFiles 5
%NSSM_PATH% set "%SERVICE_FLASK%" AppRotateOnline 1
%NSSM_PATH% set "%SERVICE_FLASK%" AppRotateBytes 1048576

rem ==== Scheduler ====
%NSSM_PATH% install "%SERVICE_SCHED%" "%PYTHON_PATH%" "%BASE_PATH%\auto_scheduler.py"
%NSSM_PATH% set "%SERVICE_SCHED%" AppDirectory "%BASE_PATH%"
%NSSM_PATH% set "%SERVICE_SCHED%" AppExit Default Restart
%NSSM_PATH% set "%SERVICE_SCHED%" Start SERVICE_AUTO_START

echo Da dang ky xong!
pause
goto menu

:start
cls
net start "%SERVICE_FLASK%"
net start "%SERVICE_SCHED%"
pause
goto menu

:remove
cls
net stop "%SERVICE_FLASK%"
net stop "%SERVICE_SCHED%"
%NSSM_PATH% remove "%SERVICE_FLASK%" confirm
%NSSM_PATH% remove "%SERVICE_SCHED%" confirm
pause
goto menu

:restart
cls
net stop "%SERVICE_FLASK%"
net stop "%SERVICE_SCHED%"
timeout /t 2 > nul
net start "%SERVICE_FLASK%"
net start "%SERVICE_SCHED%"
goto menu

:remove_all_services
cls
echo === Dang tien hanh xoa toan bo dich vu da dang ky ===

for /d %%D in (%ROOT_PATH%\montechpos_*) do (
    set "FOLDER=%%~nxD"
    call :remove_single_service "!FOLDER!"
)

echo Da huy toan bo dich vu.
pause
goto menu

:remove_single_service
set "SERVICE_FLASK=FlaskApp_%~1"
set "SERVICE_SCHED=Scheduler_%~1"
echo Dang xoa %SERVICE_FLASK% va %SERVICE_SCHED%
net stop "%SERVICE_FLASK%" >nul 2>&1
net stop "%SERVICE_SCHED%" >nul 2>&1
%nssm_path% remove "%SERVICE_FLASK%" confirm >nul 2>&1
%nssm_path% remove "%SERVICE_SCHED%" confirm >nul 2>&1
goto :eof

