@echo off
title QUAN LY NSSM CHO NGINX
chcp 65001 > nul

REM === Cau hinh ===
set "NGINX_EXE=C:\nginx-1.28.0\nginx.exe"
set "NGINX_DIR=C:\nginx-1.28.0"
set "NSSM_EXE=C:\nssm-2.24\win64\nssm.exe"
set "SERVICE_NAME=nginx"

REM === Kiem tra file ===
if not exist "%NGINX_EXE%" (
    echo Loi: Khong tim thay nginx.exe tai: %NGINX_EXE%
    pause
    exit /b
)
if not exist "%NSSM_EXE%" (
    echo Loi: Khong tim thay nssm.exe tai: %NSSM_EXE%
    pause
    exit /b
)

:MENU
cls
echo ===============================================
echo        QUAN LY NGINX BANG NSSM v3.1
echo ===============================================
echo 1. Cai dat nginx thanh service
echo 2. Khoi dong nginx
echo 3. Xem port nginx va flask dang lang nghe
echo 4. Kiem tra trang thai va domain
echo 5. Reload nginx
echo 6. Huy service va tat nginx
echo 7. Kiem tra tien trinh dang giu port 80
echo 0. Thoat
echo ===============================================
set /p CHOICE=Nhap lua chon (0-6):

if "%CHOICE%"=="1" goto CAIDAT
if "%CHOICE%"=="2" goto START
if "%CHOICE%"=="3" goto PORT
if "%CHOICE%"=="4" goto TRANGTHAI
if "%CHOICE%"=="5" goto RELOAD
if "%CHOICE%"=="6" goto XOA
if "%CHOICE%"=="7" goto KIEMTRAPORT
if "%CHOICE%"=="0" goto THOAT

goto MENU

:CAIDAT
echo Dang cai dat service nginx...
"%NSSM_EXE%" install %SERVICE_NAME% "%NGINX_EXE%"
"%NSSM_EXE%" set %SERVICE_NAME% AppDirectory "%NGINX_DIR%"
"%NSSM_EXE%" set %SERVICE_NAME% Start SERVICE_AUTO_START
echo Da cai dat thanh cong.
pause
goto MENU

:START
"%NSSM_EXE%" start %SERVICE_NAME%
echo Da khoi dong nginx.
pause
goto MENU

:PORT
echo Dang hien thi cac port dang lang nghe (80, 251xx)...
netstat -ano | findstr "LISTENING" | findstr ":80 :251"
pause
goto MENU

:TRANGTHAI
echo Trang thai service nginx:
sc query %SERVICE_NAME% | findstr /i STATE
echo.

echo Cac domain dang phuc vu (tu nginx.conf):
type "%NGINX_DIR%\conf\nginx.conf" | findstr /i "server_name"
type "%NGINX_DIR%\conf\nginx.conf" | findstr /i "listen"
pause
goto MENU

:RELOAD
echo Dang reload nginx (restart service)...
"%NSSM_EXE%" restart %SERVICE_NAME%
echo Da restart nginx xong.
pause
goto MENU

:XOA
"%NSSM_EXE%" stop %SERVICE_NAME%
"%NSSM_EXE%" remove %SERVICE_NAME% confirm
taskkill /f /im nginx.exe > nul 2>&1
echo Da xoa service va tat nginx.
pause
goto MENU

:KIEMTRAPORT
echo Danh sach tien trinh dang su dung port 80:
echo Neu khong phai nginx. Vao services.msc de Disable:
for /f "tokens=5" %%a in ('netstat -aon ^| find ":80" ^| find "LISTENING"') do (
    echo PID: %%a
    tasklist /FI "PID eq %%a"
)
pause
goto MENU

:THOAT
echo Tam biet!
timeout /t 1 > nul
exit
