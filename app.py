from flask import Flask, request, render_template, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import CheckConstraint
from sqlalchemy import text
import traceback
import time
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

import logging
from logging.handlers import RotatingFileHandler
import os

import threading

latest_ipwan = {"ip": None, "time": None}
#-----------------------------------------------------------------------------------------------------------------
os.makedirs("debug/logs_app", exist_ok=True)
logger = logging.getLogger("app")
logger.setLevel(logging.INFO)

if not logger.handlers:
    handler = RotatingFileHandler("debug/logs_app/app.log", maxBytes=1_000_000, backupCount=10, encoding="utf-8")
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
#-----------------------------------------------------------------------------------------------------------------
app = Flask(__name__)
app.secret_key = 'montechpos_77997799' # Dùng chung tất cả trạm
#-----------------------------------------------------------------------------------------------------------------
# Đọc cấu hình từ file config/app_config.json
import json
with open("config/app_config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

APP_PORT = config.get("APP_PORT", 80)
APP_ID = config.get("APP_ID", "demo")
STORE_CODE = config.get("STORE_CODE", "S01")
MONBOX_URL = config.get("MONBOX_URL", "http://*************:25123")
MONBOX_AUTH = tuple(config.get("MONBOX_AUTH", ["admin", "25123"]))
#-----------------------------------------------------------------------------------------------------------------

app.config['SESSION_COOKIE_NAME'] = f"{APP_ID}_session"
app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+pymysql://montechpos:77997799@localhost/{APP_ID}?charset=utf8mb4'

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = 1 * 1024 * 1024  # Giới hạn 1MB

db = SQLAlchemy(app)

class Station(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    stationCode = db.Column(db.String(20), unique=True, nullable=False)  # ví dụ: S01, S02
    stationName = db.Column(db.String(100), nullable=False)              # ví dụ: CHXD 01

class Log(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    dataType = db.Column(db.Integer, default=2)
    verificationCode = db.Column(db.String(50), default="N/A")
    transactionCode = db.Column(db.String(30), unique=True, nullable=False)
    partnerCode = db.Column(db.String(20), default="P01")
    companyCode = db.Column(db.String(20), default="C01")
    storeCode = db.Column(db.String(20), default="S01")
    station_id = db.Column(db.Integer, db.ForeignKey('station.id'), nullable=True)
    hardwareId = db.Column(db.String(12), default="N/A")
    faucetNo = db.Column(db.Integer, default=0)
    pumpSerial = db.Column(db.Integer, default=0)
    fuelType = db.Column(db.String(10), default="N/A")
    transactionDate = db.Column(db.String(10), default="N/A")
    transactionTime = db.Column(db.String(8), default="N/A")
    transactionCost = db.Column(db.Integer, default=0)
    transactionAmount = db.Column(db.Integer, default=0)
    transactionPrice = db.Column(db.Integer, default=0)
    shiftCost = db.Column(db.BigInteger, default=0)
    shiftAmount = db.Column(db.BigInteger, default=0)
    shiftNumberOfTransactions = db.Column(db.Integer, default=0)
    totalCost = db.Column(db.BigInteger, default=0)
    totalAmount = db.Column(db.BigInteger, default=0)
    totalNumberOfTransactions = db.Column(db.Integer, default=0)
    isInvoiced = db.Column(db.Boolean, default=False)
    invoiceNumber = db.Column(db.String(50))
    internalNote = db.Column(db.Text)
    provider = db.Column(db.String(20))
    invoiceId = db.Column(db.String(50))
    qr_paid = db.Column(db.Boolean, default=False)
 
    station = db.relationship("Station")


    __table_args__ = (
        CheckConstraint('transactionCost BETWEEN 0 AND **********'),
        CheckConstraint('transactionAmount BETWEEN 0 AND **********'),
        CheckConstraint('transactionPrice BETWEEN 0 AND **********'),
        CheckConstraint('shiftCost BETWEEN 0 AND **********'),
        CheckConstraint('shiftAmount BETWEEN 0 AND **********'),
        CheckConstraint('shiftNumberOfTransactions BETWEEN 0 AND **********'),
        CheckConstraint('totalCost BETWEEN 0 AND **********'),
        CheckConstraint('totalAmount BETWEEN 0 AND **********'),
        CheckConstraint('totalNumberOfTransactions BETWEEN 0 AND **********'),
    )
    
class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)  # STT
    mkhang = db.Column(db.String(50))  # Mã KH (có thể trống, không unique)
    buyer = db.Column(db.String(100))
    company = db.Column(db.String(200))
    address = db.Column(db.String(300))
    tax = db.Column(db.String(20))
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    plate = db.Column(db.String(20))
    mdvqhns = db.Column(db.String(20))

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(10), default="user")  # 'admin' hoặc 'user'
    
    api_account = db.Column(db.String(255))
    api_pass = db.Column(db.String(255))  

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class InvoiceLog(db.Model):
    __tablename__ = "invoice_log"

    id = db.Column(db.Integer, primary_key=True)
    transaction_code = db.Column(db.String(50), nullable=False)
    invoice_number = db.Column(db.String(50), nullable=False)
    custom_data = db.Column(db.JSON)  # Lưu toàn bộ dữ liệu khách hàng từ frontend
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
#------------------ Xử lý nhận log từ Monbox ------------------
@app.route('/montechpos', methods=['POST'])
def receive_log():
    try:
        data = request.get_json()
        # Ghi nhớ IP WAN MonBox mới nhất
        ip_wan = request.remote_addr
        latest_ipwan["ip"] = ip_wan
        latest_ipwan["time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"🌐 IP WAN MonBox: {ip_wan}")

        logger.info(f"\n📥 [RECEIVED] Dữ liệu JSON từ MonBox: {data}")

        if not data:
            logger.info("❌ Không có JSON hợp lệ trong request.")
            return jsonify({"code": 105, "message": "Du lieu khong hop le"}), 200

        transaction_code = data.get("transactionCode")
        if not transaction_code:
            logger.info("❌ Thiếu transactionCode trong dữ liệu.")
            return jsonify({"code": 105, "message": "Thieu ma giao dich"}), 200

        logger.info(f"🔍 Kiểm tra trùng transactionCode: {transaction_code}")
        existing_log = Log.query.filter_by(transactionCode=transaction_code).first()
        if existing_log:
            logger.info(f"⚠️ Bỏ qua: Giao dịch đã tồn tại với transactionCode: {transaction_code}")
            return jsonify({
                "code": 101,
                "message": "Giao dich da ton tai",
                "data": {
                    "dataType": existing_log.dataType,
                    "transactionCode": existing_log.transactionCode,
                    "verificationCode": existing_log.verificationCode
                }
            }), 200

        # Lấy storeCode và tìm station tương ứng
        store_code = data.get("storeCode", "S01")
        station = Station.query.filter_by(stationCode=store_code).first()
        if not station:
            logger.info(f"⚠️ Không tìm thấy station tương ứng với storeCode = {store_code} => sẽ lưu log nhưng station_id=None")
        else:
            logger.info(f"✅ Tìm thấy station: {station.stationName} (ID={station.id}) cho storeCode = {store_code}")

        try:
            with open("config/tank.json", "r", encoding="utf-8") as f:
                tank_map = json.load(f)
            tank_number = tank_map.get(str(data.get("pumpSerial")), 0)
        except:
            tank_number = 0
        # Ghi log mới vào DB
        log_entry = Log(
            station_id=station.id if station else None,
            dataType=data.get("dataType", 2),
            verificationCode=data.get("verificationCode", "N/A"),
            transactionCode=transaction_code,
            partnerCode=data.get("partnerCode", "P01"),
            companyCode=data.get("companyCode", "C01"),
            storeCode=store_code,
            hardwareId=data.get("hardwareId", "N/A"),
            faucetNo=data.get("faucetNo", 0),
            pumpSerial=data.get("pumpSerial", 0),
            fuelType=data.get("fuelType", "N/A"),
            transactionDate=data.get("transactionDate", "N/A"),
            transactionTime=data.get("transactionTime", "N/A"),
            transactionCost=data.get("transactionCost", 0),
            transactionAmount=data.get("transactionAmount", 0),
            transactionPrice=data.get("transactionPrice", 0),
            shiftCost=data.get("shiftCost", 0),
            shiftAmount=data.get("shiftAmount", 0),
            shiftNumberOfTransactions=data.get("shiftNumberOfTransactions", 0),
            totalCost=data.get("totalCost", 0),
            totalAmount=data.get("totalAmount", 0),
            totalNumberOfTransactions=data.get("totalNumberOfTransactions", 0),
        )

        db.session.add(log_entry)
        db.session.commit()

        logger.info(f"📦 Ghi log thành công | Trạm: {store_code} | Mã GD: {log_entry.transactionCode} | ID: {log_entry.id}")

        return jsonify({
            "code": 0,
            "message": "Da tao giao dich thanh cong",
            "data": {
                "dataType": log_entry.dataType,
                "transactionCode": log_entry.transactionCode,
                "verificationCode": log_entry.verificationCode
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.info(f"🛑 Lỗi khi ghi log: {e}")
        logger.info(traceback.format_exc())
        return jsonify({
            "code": 105,
            "message": "Loi khi luu giao dich",
            "error": str(e),
            "trace": traceback.format_exc()
        }), 200
#------------------ Xử lý tải file csv ngày từ trang check_log bằng MONBOX_URL (link Noip) ------------------ 
@app.route("/api/import_log_by_date", methods=["POST"])
def import_log_by_date():
    from datetime import datetime
    import requests

    date_str = request.json.get("date")
    if not date_str:
        return jsonify({"success": False, "message": "Thiếu ngày"}), 400

    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    except:
        return jsonify({"success": False, "message": "Sai định dạng ngày"}), 400

    year = date_obj.strftime("%Y")
    month = date_obj.strftime("%m")
    filename = date_obj.strftime("%Y%m%d")

    url = f"{MONBOX_URL}/edit_sdfs?download=/{year}/{month}/{filename}.csv"
    try:
        response = requests.get(url, auth=MONBOX_AUTH)
        response.raise_for_status()
        content = response.content.decode("utf-8-sig")
        inserted, skipped = import_csv_from_text(content)
        return jsonify({"success": True, "inserted": inserted, "skipped": skipped})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})

#------------------ Xử lý tải file csv ngày từ trang check_log bằng IP WAN nhận được ------------------ 
@app.route("/api/import_csv_from_ip_wan", methods=["POST"])
def import_csv_from_ip_wan():
    import requests

    data = request.get_json()
    url = data.get("url")
    if not url:
        return jsonify({"success": False, "message": "Thiếu URL"}), 400

    try:
        response = requests.get(url, auth=MONBOX_AUTH, timeout=10)
        response.raise_for_status()
        content = response.content.decode("utf-8-sig")
        inserted, skipped = import_csv_from_text(content)
        return jsonify({"success": True, "inserted": inserted, "skipped": skipped})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})

#------------------ Xử lý tải file logs ngày từ trang check_log bằng uploadfile ------------------ 
@app.route("/api/import_log_by_date", methods=["POST"])
@app.route("/api/import_csv_file", methods=["POST"])
def import_csv_file():
    from io import TextIOWrapper
    from datetime import datetime
    import csv

    if 'file' not in request.files:
        return jsonify({"success": False, "message": "Không có file"}), 400

    f = request.files['file']
    reader = csv.DictReader(TextIOWrapper(f, encoding="utf-8-sig"))

    inserted, skipped = 0, 0
    station = Station.query.filter_by(stationCode=STORE_CODE).first()

    for row in reader:
        try:
            serial = str(row.get("SerialNo", "")).strip()
            date_raw = str(row.get("Date", "")).strip().replace("/", "-")
            time_raw = str(row.get("Time", "00:00:00")).strip()
            pump_no = str(row.get("PumpNo", "0")).strip()
            pump_id = str(row.get("PumpId", "0")).strip()

            date_obj = datetime.strptime(date_raw, "%Y-%m-%d")
            date_str_db = date_obj.strftime("%Y-%m-%d")
            date_for_code = date_obj.strftime("%d%m%y")
            time_for_code = time_raw.replace(":", "")
            code = f"{serial}_{date_for_code}_{time_for_code}_{pump_no}"

            if Log.query.filter_by(transactionCode=code).first():
                skipped += 1
                continue

            log_entry = Log(
                station_id=station.id,
                storeCode=STORE_CODE,
                dataType=2,
                verificationCode="AUTO",
                transactionCode=code,
                partnerCode="P01",
                companyCode="C01",
                hardwareId="MONBOX",
                faucetNo=int(pump_id),
                pumpSerial=int(serial),
                fuelType=row.get("Fuel", "N/A").strip(),
                transactionDate=date_str_db,
                transactionTime=time_raw,
                transactionCost=int(row.get("Money", 0)),
                transactionAmount=int(row.get("Liter", 0)),
                transactionPrice=int(row.get("Price", 0)),
            )
            db.session.add(log_entry)
            inserted += 1
        except:
            skipped += 1

    db.session.commit()
    return jsonify({"success": True, "inserted": inserted, "skipped": skipped})
     
#------------------ Xử lý đọc log đã lưu từ Monbox ------------------
from datetime import datetime, date

@app.route('/logs', methods=['GET'])
def get_logs():
    import json

    start = request.args.get("start")
    end = request.args.get("end")

    query = Log.query.filter(Log.station_id.isnot(None))

    # Xác định ngày & giới hạn log
    max_per_day = 7000
    max_total = 630000

    if not start and not end:
        today = date.today().strftime("%Y-%m-%d")
        query = query.filter(Log.transactionDate == today)
        limit = max_per_day
        logger.info(f"📅 [GET /logs] Không truyền ngày → chỉ lấy log hôm nay: {today}")
    else:
        try:
            d1 = datetime.strptime(start, "%Y-%m-%d") if start else date.today()
            d2 = datetime.strptime(end, "%Y-%m-%d") if end else date.today()
            days = (d2 - d1).days + 1
            limit = min(days * max_per_day, max_total)

            if start:
                query = query.filter(Log.transactionDate >= start)
            if end:
                query = query.filter(Log.transactionDate <= end)

            logger.info(f"📅 [GET /logs] Lọc theo khoảng ngày: từ {start or '...'} đến {end or '...'} ({days} ngày), limit {limit}")
        except Exception as e:
            logger.warning(f"Lỗi phân tích ngày: {e}")
            limit = max_per_day  # fallback

    logs = query.order_by(Log.id.desc()).limit(limit).all()

    # Đọc fuel map
    try:
        with open("config/fuel.json", "r", encoding="utf-8") as f:
            fuel_map = json.load(f)
    except:
        fuel_map = {}

    # Truy custom_data theo batch
    codes = [log.transactionCode for log in logs]
    custom_data_map = {}
    if codes:
        result = db.session.execute(
            text("SELECT transaction_code, custom_data FROM invoice_log WHERE transaction_code IN :codes"),
            {"codes": tuple(codes)}
        ).fetchall()
        for code, data in result:
            try:
                custom_data_map[code] = json.loads(data) if data else {}
            except:
                custom_data_map[code] = {}

    # Chuẩn bị log trả về
    log_list = []
    for log in logs:
        custom_data = custom_data_map.get(log.transactionCode, {})
        log_list.append({
            "transactionCode": log.transactionCode,
            "transactionDate": log.transactionDate,
            "transactionTime": log.transactionTime,
            "faucetNo": log.faucetNo,
            "pumpSerial": log.pumpSerial,
            "transactionCost": log.transactionCost,
            "transactionAmount": log.transactionAmount,
            "transactionPrice": log.transactionPrice,
            "fuelType": fuel_map.get(str(log.fuelType).strip().upper(), log.fuelType),
            "shiftCost": log.shiftCost,
            "shiftAmount": log.shiftAmount,
            "shiftNumberOfTransactions": log.shiftNumberOfTransactions,
            "totalCost": log.totalCost,
            "totalAmount": log.totalAmount,
            "totalNumberOfTransactions": log.totalNumberOfTransactions,
            "isInvoiced": log.isInvoiced in [1, True],
            "invoiceNumber": log.invoiceNumber,
            "stationCode": log.storeCode,
            "stationName": log.station.stationName if log.station else "",
            "invoiceId": log.invoiceId,
            "provider": log.provider,
            "qr_paid": log.qr_paid,
            "custom_data": custom_data
        })

    logger.info(f"✅ [GET /logs] Tổng số log trả về: {len(log_list)}")
    return jsonify({"logs": log_list})

#------------------ Xử lý xóa log đã lưu từ Monbox ------------------
@app.route('/reset_logs', methods=['POST'])
def reset_logs():
    token = request.args.get('token')
    if token != '25123':
        return jsonify({"code": 403, "message": "Sai mat khau"}), 403

    try:
        num_deleted = db.session.query(Log).delete()
        db.session.commit()
        return jsonify({"code": 0, "message": f"Da xoa {num_deleted} giao dich."}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({"code": 500, "message": "Loi khi xoa giao dich.", "error": str(e)}), 500


#------------------ Xử lý hiển thị log và giao diện trang home ------------------
@app.route('/')
def index():
    username = session.get("username", "")
    is_admin = session.get("role") == "admin"

    if username == "banhang":
        return render_template('index_qr.html', is_admin=is_admin)

    user_agent = request.headers.get('User-Agent', '').lower()
    is_mobile = 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent

    if is_mobile:
        return render_template('index_mobile.html', is_admin=is_admin)

    return render_template('index.html', is_admin=is_admin)

#------------------ web bản mobile -------------------------
@app.route('/index_mobile')
def index_mobile():
    return render_template('index_mobile.html')
#------------------ web bản QR ------------------
@app.route('/index_qr')
def index_qr():
    return render_template("index_qr.html")

#------------------ Xử lý kiếm tra thiết bị có đang xuất hóa đơn ? ------------------
from threading import Lock

app.config['INVOICE_LOCK'] = Lock()
app.config['isInvoiceProcessing'] = False


@app.route("/api/invoice-lock", methods=["GET", "POST"])
def invoice_lock():
    if request.method == "POST":
        status = request.json.get("isProcessing", False)
        with app.config['INVOICE_LOCK']:
            app.config['isInvoiceProcessing'] = status
        return jsonify({"success": True})
    else:
        return jsonify({"isProcessing": app.config['isInvoiceProcessing']})

#------------------ Xử lý xuất hóa đơn ------------------
from send_invoice import send_invoice_from_log, send_invoice_from_log_with_provider

@app.route('/create_invoice', methods=['POST'])
def create_invoice():
    try:
        data = request.get_json()
        code = data.get("transactionCode")
        custom_data = data.get("customData", {})

        if not code:
            return jsonify({"success": False, "message": "Thiếu mã giao dịch"}), 400

        # Đọc config để xác định nhà cung cấp đang dùng
        import json, os
        config_path = os.path.join("config", "config.json")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        provider = config.get("provider", "vnpt").lower()

        # Gọi từng hàm theo provider
        if provider == "vnpt":
            return create_invoice_vnpt(code, custom_data)
        elif provider == "mobifone":
            return create_invoice_mobifone(code, custom_data)
        elif provider == "viettel":
            return create_invoice_viettel(code, custom_data)            
        elif provider == "misa":
            return create_invoice_misa(code, custom_data)
        elif provider == "easy":
            return create_invoice_easy(code, custom_data)              
        elif provider == "bkav":
            return create_invoice_bkav(code, custom_data)
        elif provider == "fast":
            return create_invoice_fast(code, custom_data)  
        elif provider == "hilo":
            return create_invoice_hilo(code, custom_data)             
        elif provider == "mobifone_test":
            return create_invoice_mobifone_test(code, custom_data)
        else:
            return jsonify({"success": False, "message": f"Chưa hỗ trợ provider: {provider}"}), 400

    except Exception as e:
        import traceback
        logger.info(f"❌ Lỗi tại /create_invoice: {str(e)}")
        logger.info(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)}), 500

# ================= ROUTE MỚI CHO LOG GỘP =================
@app.route('/create_invoice_from_preview', methods=['POST'])
def create_invoice_from_preview():
    try:
        data = request.get_json()
        preview_data = data.get("previewData")
        custom_data = data.get("customData", {})
        gopFromCodes = data.get("gopFromCodes", [])  # Lấy danh sách log gộp

        if not preview_data:
            return jsonify({"success": False, "message": "Thiếu dữ liệu previewData"}), 400

        # Ghép gopFromCodes vào preview_data để phía sau xử lý đúng
        preview_data["gopFromCodes"] = gopFromCodes

        # Đọc provider
        import json, os
        config_path = os.path.join("config", "config.json")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        provider = config.get("provider", "vnpt").lower()

        # Gộp cho vnpt         
        if provider == "vnpt":
            from send_invoice import create_invoice_vnpt_from_preview
            result = create_invoice_vnpt_from_preview(preview_data, custom_data)
            return jsonify(result)  
            
        # Gộp cho mobifone
        elif provider == "mobifone":
            from send_invoice import create_invoice_mobifone_from_preview
            result = create_invoice_mobifone_from_preview(preview_data, custom_data)
            return jsonify(result)
            
        # Gộp cho viettel
        elif provider == "viettel":
            from send_invoice import create_invoice_viettel_from_preview
            result = create_invoice_viettel_from_preview(preview_data, custom_data)
            return jsonify(result)            
            
        # Gộp cho misa
        elif provider == "misa":
            from send_invoice import create_invoice_misa_from_preview
            result = create_invoice_misa_from_preview(preview_data, custom_data)
            return jsonify(result)            
             
        # Gộp cho easy
        elif provider == "easy":
            from send_invoice import create_invoice_easy_from_preview
            result = create_invoice_easy_from_preview(preview_data, custom_data)
            return jsonify(result)           
            
        # Gộp cho bkav
        elif provider == "bkav":
            from send_invoice import create_invoice_bkav_from_preview
            result = create_invoice_bkav_from_preview(preview_data, custom_data)
            return jsonify(result)            
            
        # Gộp cho fast
        elif provider == "fast":
            from send_invoice import create_invoice_fast_from_preview
            result = create_invoice_fast_from_preview(preview_data, custom_data)
            return jsonify(result)             
             
        # Gộp cho hilo
        elif provider == "hilo":
            from send_invoice import create_invoice_hilo_from_preview
            result = create_invoice_hilo_from_preview(preview_data, custom_data)
            return jsonify(result)            
                        
        # Gộp cho mobifone_test           
        elif provider == "mobifone_test":
            from send_invoice import create_invoice_mobifone_test_from_preview
            result = create_invoice_mobifone_test_from_preview(preview_data, custom_data)
            return jsonify(result)                         

        else:
            return jsonify({"success": False, "message": f"Chưa hỗ trợ gộp cho {provider}"}), 400

    except Exception as e:
        import traceback
        logger.info(f"❌ Lỗi tại /create_invoice_from_preview: {str(e)}")
        logger.info(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)}), 500

        
#------------------ Xử lý xuất hóa đơn VNPT ------------------
from flask import session 

def create_invoice_vnpt(code, custom_data):
    log = db.session.query(Log).with_for_update().filter_by(transactionCode=code).first()

    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"}), 404

    if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() != ""):
        return jsonify({"success": False, "message": "Giao dịch đã được xuất hóa đơn"}), 400

    from send_invoice import send_invoice_from_log

    # Debug session info
    logger.info(f"🔍 DEBUG SESSION: username={session.get('username')}")
    logger.info(f"🔍 DEBUG SESSION: api_account={session.get('api_account')}")
    logger.info(f"🔍 DEBUG SESSION: api_pass={session.get('api_pass')}")

    response_text = send_invoice_from_log(
        log,
        custom_data=custom_data,
        api_account=session.get("api_account"),  # truyền vào nếu có
        api_pass=session.get("api_pass")
    )

    if "<ImportAndPublishInvMTTResult>" in response_text and "ERR" not in response_text:
        log.isInvoiced = True

        import re
        match = re.search(r"([0-9]{10,11})", response_text)
        if match:
            log.verificationCode = match.group(1)
        else:
            log.verificationCode = "CHUA_RO_SOHĐ"

        log.invoiceNumber = log.verificationCode

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "Đã gửi hóa đơn VNPT thành công",
            "response": response_text,
            "shdon": log.verificationCode
        })
    else:
        return jsonify({"success": False, "message": "VNPT trả lỗi: " + response_text})

#------------------ Xử lý xuất hóa đơn Mobifone ------------------
def create_invoice_mobifone(code, custom_data):
    log = db.session.query(Log).with_for_update().filter_by(transactionCode=code).first()

    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"}), 404

    if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() != ""):
        return jsonify({"success": False, "message": "Giao dịch đã được xuất hóa đơn"}), 400

    from send_invoice import send_invoice_from_log
    result = send_invoice_from_log(log, custom_data=custom_data)

    if result.get("success"):
        invoice_id = result.get("invoiceId") or result.get("idHDDT") or result.get("hdon_id")

        if result.get("shdon"):
            log.verificationCode = result["shdon"]
            log.invoiceNumber = result["shdon"]
            log.isInvoiced = True

        # Ghi provider và invoiceId nếu có
        log.provider = "mobifone"
        if invoice_id:
            log.invoiceId = invoice_id

        db.session.commit()

        # Trả thêm viewUrl nếu có invoiceId
        result["invoiceId"] = log.invoiceId
        result["provider"] = "mobifone"
        if log.invoiceId:
            result["viewUrl"] = f"http://mobiinvoice.vn:9000/{log.invoiceId}"

        return jsonify(result)
    else:
        return jsonify(result), 500

#------------------ Xử lý xuất hóa đơn Mobifone_test ------------------
def create_invoice_mobifone_test(code, custom_data):
    log = db.session.query(Log).with_for_update().filter_by(transactionCode=code).first()

    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"}), 404

    if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() != ""):
        return jsonify({"success": False, "message": "Giao dịch đã được xuất hóa đơn"}), 400

    from send_invoice import send_invoice_from_log
    result = send_invoice_from_log(log, custom_data=custom_data)

    if result.get("success"):
        invoice_id = result.get("invoiceId") or result.get("idHDDT") or result.get("hdon_id")

        if result.get("shdon"):
            log.verificationCode = result["shdon"]
            log.invoiceNumber = result["shdon"]
            log.isInvoiced = True

        # Ghi provider và invoiceId nếu có
        log.provider = "mobifone_test"
        if invoice_id:
            log.invoiceId = invoice_id

        db.session.commit()

        # Trả thêm viewUrl nếu có invoiceId
        result["invoiceId"] = log.invoiceId
        result["provider"] = "mobifone_test"
        if log.invoiceId:
            result["viewUrl"] = f"http://mobiinvoice.vn:9000/{log.invoiceId}"

        return jsonify(result)
    else:
        return jsonify(result), 500

#------------------ Xử lý xuất hóa đơn Bkav ------------------
def create_invoice_bkav(code, custom_data):
    log = db.session.query(Log).with_for_update().filter_by(transactionCode=code).first()

    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"}), 404

    if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() != ""):
        return jsonify({"success": False, "message": "Giao dịch đã được xuất hóa đơn"}), 400

    # Đọc config Bkav
    try:
        with open("config/config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        conf = config.get("BKAV", {})
    except Exception as e:
        return jsonify({"success": False, "message": f"Lỗi đọc config: {e}"}), 500

    from send_invoice import handle_bkav
    result = handle_bkav(log, preview=False, custom_data=custom_data, conf=conf)

    if result.get("success"):
        invoice_no = result.get("invoiceNo") or result.get("invoiceNumber")

        if invoice_no:
            log.verificationCode = str(invoice_no)
            log.invoiceNumber = str(invoice_no)
            log.isInvoiced = True
            log.provider = "bkav"
            log.invoiceId = str(invoice_no)

        db.session.commit()

        # Trả về kết quả
        result["invoiceId"] = log.invoiceId
        result["provider"] = "bkav"

        return jsonify(result)
    else:
        return jsonify(result), 500

#------------------ API lấy URL PDF từ BKAV ------------------
@app.route('/api/bkav_pdf_url', methods=['POST'])
def get_bkav_pdf_url():
    import requests
    import base64

    try:
        data = request.get_json()
        transaction_code = data.get("transactionCode")

        if not transaction_code:
            return jsonify({"success": False, "message": "Thiếu mã giao dịch"}), 400

        # Đọc config BKAV
        try:
            with open("config/config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            conf = config.get("BKAV", {})
        except Exception as e:
            return jsonify({"success": False, "message": f"Lỗi đọc config: {e}"}), 500

        # Lấy thông tin cấu hình
        partner_guid = conf.get("partnerGUID")
        api_url = conf.get("url_api_link_test") if conf.get("env_testing") == "ON" else conf.get("url_api_link")
        pdf_base_url = conf.get("url_link_PDF", "https://demo.ehoadon.vn")

        if not partner_guid or not api_url:
            return jsonify({"success": False, "message": "Thiếu cấu hình BKAV"}), 500

        # Tạo command data để lấy thông tin PDF
        command_data = {
            "CmdType": 816,
            "CommandObject": [{
                "PartnerInvoiceID": 0,
                "PartnerInvoiceStringID": transaction_code
            }]
        }

        # Mã hóa command data
        command_json = json.dumps(command_data, ensure_ascii=False)
        command_base64 = base64.b64encode(command_json.encode('utf-8')).decode('utf-8')

        # Tạo payload gửi đi
        payload = {
            "partnerGUID": partner_guid,
            "CommandData": command_base64
        }

        # Headers
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "MontechPos/1.0",
            "Accept": "application/json"
        }

        # Gửi request đến BKAV
        logger.info(f"🔍 [BKAV PDF] Gửi request lấy PDF cho: {transaction_code}")
        response = requests.post(api_url, json=payload, headers=headers, timeout=30)

        if response.status_code != 200:
            return jsonify({"success": False, "message": f"BKAV API lỗi: {response.status_code}"}), 500

        # Xử lý response
        response_data = response.json()

        if isinstance(response_data, dict) and "d" in response_data:
            # Decode base64 response
            decoded = base64.b64decode(response_data["d"]).decode("utf-8")
            actual_response = json.loads(decoded)

            logger.info(f"📥 [BKAV PDF] Response: {json.dumps(actual_response, indent=2, ensure_ascii=False)}")

            # Kiểm tra response thành công
            if actual_response.get("isOk") and actual_response.get("Object"):
                object_data = actual_response.get("Object")

                # Parse Object nếu là string
                if isinstance(object_data, str):
                    objects = json.loads(object_data)
                else:
                    objects = object_data

                if isinstance(objects, list) and len(objects) > 0:
                    mess_log = objects[0].get("MessLog", "")

                    if mess_log and mess_log.startswith("/"):
                        # Loại bỏ dấu "/" đầu tiên và tạo URL đầy đủ
                        pdf_path = mess_log[1:] if mess_log.startswith("/") else mess_log
                        full_pdf_url = f"{pdf_base_url}/{pdf_path}"

                        logger.info(f"✅ [BKAV PDF] URL thành công: {full_pdf_url}")
                        return jsonify({"success": True, "pdfUrl": full_pdf_url})
                    else:
                        return jsonify({"success": False, "message": "Không tìm thấy đường dẫn PDF trong response"})
                else:
                    return jsonify({"success": False, "message": "Response không chứa dữ liệu hóa đơn"})
            else:
                error_msg = actual_response.get("Code") or "Lỗi không xác định từ BKAV"
                return jsonify({"success": False, "message": f"BKAV trả lỗi: {error_msg}"})
        else:
            return jsonify({"success": False, "message": "Format response không đúng"})

    except Exception as e:
        logger.error(f"❌ [BKAV PDF] Lỗi: {str(e)}")
        return jsonify({"success": False, "message": f"Lỗi hệ thống: {str(e)}"}), 500



#------------------ Xử lý xuất hóa đơn Viettel ------------------
def create_invoice_viettel(code, custom_data):
    log = db.session.query(Log).with_for_update().filter_by(transactionCode=code).first()

    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"}), 404

    if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() != ""):
        return jsonify({"success": False, "message": "Giao dịch đã được xuất hóa đơn"}), 400

    # Đọc config Viettel
    try:
        with open("config/config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        conf = config.get("VIETTEL", {})
    except Exception as e:
        return jsonify({"success": False, "message": f"Lỗi đọc config: {e}"}), 500

    from send_invoice import handle_viettel
    result = handle_viettel(log, preview=False, custom_data=custom_data, conf=conf)

    if result.get("success"):
        # ✅ FIX: Update database at app.py level like BKAV/VNPT for consistency
        invoice_no = result.get("invoiceNo") or result.get("invoiceNumber")

        if invoice_no:
            log.verificationCode = str(invoice_no)
            log.invoiceNumber = str(invoice_no)
            log.isInvoiced = True
            log.provider = "viettel"
            log.invoiceId = str(invoice_no)

        db.session.commit()

        # Trả về kết quả
        result["invoiceId"] = log.invoiceId
        result["provider"] = "viettel"

        return jsonify(result)
    else:
        return jsonify(result), 500

#------------------ Xử lý xem trước hóa đơn Popup ------------------
@app.route('/preview_invoice', methods=['POST'])
def preview_invoice():
    data = request.get_json()
    code = data.get("transactionCode")
    provider = data.get("provider", "").upper()  # Nhận provider từ frontend
    custom_data = data.get("customData", {})

    log = db.session.query(Log).filter_by(transactionCode=code).first()
    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"})

    try:
        # Nếu có provider cụ thể, sử dụng nó; nếu không thì dùng config mặc định
        if provider:
            result = send_invoice_from_log_with_provider(log, provider, preview=True, custom_data=custom_data)
        else:
            result = send_invoice_from_log(log, preview=True, custom_data=custom_data)
        return jsonify(result)
    except Exception as e:
        logger.info(f"🔥 Lỗi khi preview: {e}")
        return jsonify({"success": False, "message": str(e)})


#------------------ Xử lý xuất GD ra Excel theo bảng lọc ------------------
from flask import send_file, request, jsonify
from openpyxl import Workbook
from io import BytesIO
import json
import os
# Load map fuel từ file fuel.json
fuel_map = {}
try:
    with open(os.path.join("config", "fuel.json"), encoding="utf-8") as f:
        fuel_map = json.load(f)
except Exception as e:
    logger.info(f"❌ Không thể load fuel.json: {e}")


# Load map tank từ file tank.json
tank_map = {}
try:
    with open(os.path.join("config", "tank.json"), encoding="utf-8") as f:
        tank_map = json.load(f)
except Exception as e:
    logger.info(f"❌ Không thể load tank.json: {e}") 
    
#------------------ Xử lý xuất GD ra Excel theo bảng lọc ------------------
from datetime import datetime
@app.route('/export_logs_excel', methods=['POST'])
def export_logs_excel():
    try:
        data = request.get_json()
        codes = data.get("codes", [])

        if not codes:
            return jsonify(success=False, message="Không có giao dịch nào được chọn"), 400

        logs = Log.query.filter(Log.transactionCode.in_(codes)).all()
        if not logs:
            return jsonify(success=False, message="Không tìm thấy giao dịch phù hợp"), 400

        wb = Workbook()
        ws = wb.active
        ws.title = "Logs"

        ws.append([
            "Ngày", "Giờ", "Vòi", "Nhiên liệu", "Số tiền", "Số lít", "Đơn giá",
            "Serial", "Bồn", "Mã giao dịch", "Tạo hóa đơn", "Số hóa đơn", "Chuyển khoản",
            "Mã KH", "Họ tên", "Tên", "MST", "Email", "Điện thoại", "CCCD","Mã ĐVQHNS","Biển số", "Địa chỉ"
        ])

        for log in logs:
            # ✅ Truy dữ liệu custom_data từ bảng invoice_log
            custom_data = {}
            try:
                result = db.session.execute(
                    text("SELECT custom_data FROM invoice_log WHERE transaction_code = :code"),
                    {"code": log.transactionCode}
                ).fetchone()
                if result and result[0]:
                    custom_data = json.loads(result[0])
            except Exception as e:
                logger.warning(f"[export_excel] Lỗi lấy custom_data cho {log.transactionCode}: {e}")

            row_data = [
                log.transactionDate,
                log.transactionTime,
                log.faucetNo,
                fuel_map.get(str(log.fuelType).strip().upper(), log.fuelType),
                log.transactionCost,
                log.transactionAmount / 1000,
                log.transactionPrice,
                log.pumpSerial,
                tank_map.get(str(log.pumpSerial).strip().upper(), ""),
                log.transactionCode,
                "Nội bộ" if log.invoiceNumber == "Nội bộ" else ("Đã xuất" if log.isInvoiced else "Chưa"),
                log.invoiceNumber or "",
                "Đã CK" if getattr(log, "qr_paid", False) else "Chưa",
                # Dữ liệu khách hàng
                custom_data.get("mkhang", ""),
                custom_data.get("ho_ten", ""),
                custom_data.get("ten", ""),
                custom_data.get("mst", ""),
                custom_data.get("email", ""),
                custom_data.get("phone", ""),
                custom_data.get("cccd", ""),
                custom_data.get("mdvqhns", ""),              
                custom_data.get("plate", ""),
                custom_data.get("dchi", "")
            ]

            ws.append(row_data)

            # Format ngày
            date_cell = ws.cell(row=ws.max_row, column=1)
            try:
                if isinstance(log.transactionDate, str):
                    date_cell.value = datetime.strptime(log.transactionDate, "%Y-%m-%d")
                date_cell.number_format = 'DD/MM/YYYY'
            except:
                pass

        output = BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            download_name="logs_filtered.xlsx",
            as_attachment=True,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    except Exception as e:
        logger.error(f"❌ [export_excel] Lỗi: {e}")
        return jsonify(success=False, message=str(e)), 500

#------------------ Xử lý thêm GD nội bộ ------------------
@app.route('/api/mark_internal_logs', methods=['POST'])
def mark_internal_logs():
    data = request.get_json()
    code = data.get("code")
    note = data.get("note", "")

    if not code or not note:
        return jsonify(success=False, message="Thiếu mã giao dịch hoặc lý do"), 400

    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy giao dịch"), 404

    log.invoiceNumber = "Nội bộ"
    log.verificationCode = "INTERNAL"
    log.isInvoiced = True
    log.internalNote = note
    db.session.commit()
    return jsonify(success=True)


@app.route('/api/internal_note')
def get_internal_note():
    code = request.args.get("code")
    if not code:
        return jsonify(success=False, message="Thiếu mã giao dịch")

    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy log")

    return jsonify(success=True, note=log.internalNote or "")


@app.route('/api/clear_internal_note', methods=['POST'])
def clear_internal_note():
    data = request.get_json()
    code = data.get("code")

    if not code:
        return jsonify(success=False, message="Thiếu mã giao dịch")

    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy log")

    # Bỏ trạng thái nội bộ
    log.invoiceNumber = None
    log.verificationCode = None
    log.isInvoiced = False
    log.internalNote = None
    db.session.commit()

    return jsonify(success=True)

#------------------ Gọi trang Debug API ------------------
@app.route('/debug_api')
def serve_debug_api():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template('debug_api.html')

#------------------ Gọi trang Setting cài đặt người dùng ------------------
@app.route('/setting')
def serve_setting():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template('setting.html')
    

#------------------ Gọi file Setting.json cài đặt người dùng ------------------
from flask import render_template
@app.route("/api/setting", methods=["GET"])
def get_global_setting():
    path = os.path.join("config", "settings.json")
    if os.path.exists(path):
        with open(path, "r", encoding="utf-8") as f:
            return jsonify(json.load(f))
    return jsonify({})

@app.route("/api/setting", methods=["POST"])
def save_global_setting():
    data = request.get_json()
    path = os.path.join("config", "settings.json")
    with open(path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return jsonify(success=True)

 
    
#------------------ Gọi trang Quản lý khách hàng ------------------
@app.route("/customer")
def customer_page():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template("customer.html")

#------------------ Xử lý quản lý khách hàng từ db ------------------
from flask import request, jsonify

@app.route("/api/customers", methods=["GET"])
def get_customers():
    customers = Customer.query.all()
    result = []
    for c in customers:
        result.append({
            "id": c.id,
            "mkhang": c.mkhang,
            "buyer": c.buyer,
            "company": c.company,
            "address": c.address,
            "tax": c.tax,
            "email": c.email,
            "phone": c.phone,
            "plate": c.plate,
            "mdvqhns" : c.mdvqhns
        })
    return jsonify(result)

@app.route("/api/customers", methods=["POST"])
def save_customer():
    data = request.get_json()
    mkhang = data.get("mkhang", "").strip()
    tax = data.get("tax", "").strip()

    if not mkhang:
        return jsonify(success=False, message="Thiếu mã khách hàng"), 400

    customer = None

    # Nếu có ID → cập nhật
    if "id" in data:
        customer = Customer.query.get(data["id"])

    # Nếu không có ID:
    if not customer:
        if mkhang == "KHONGMA":
            # Cho phép nhiều KHONGMA nếu khác MST
            customer = Customer.query.filter_by(mkhang="KHONGMA", tax=tax).first()
        else:
            # Với các mã khác → chỉ 1 bản duy nhất
            customer = Customer.query.filter_by(mkhang=mkhang).first()

    # Nếu vẫn chưa có → tạo mới
    if not customer:
        customer = Customer()
        db.session.add(customer)

    # Gán dữ liệu
    customer.mkhang = mkhang
    customer.buyer = data.get("buyer", "")
    customer.company = data.get("company", "")
    customer.address = data.get("address", "")
    customer.tax = tax
    customer.email = data.get("email", "")
    customer.phone = data.get("phone", "")
    customer.plate = data.get("plate", "")
    customer.mdvqhns = data.get("mdvqhns", "")    

    db.session.commit()
    return jsonify(success=True, id=customer.id)



@app.route("/api/customers/<id>", methods=["DELETE"])
def delete_customer(id):
    Customer.query.filter_by(id=id).delete()
    db.session.commit()
    return jsonify(success=True)
    
from sqlalchemy import or_

@app.route("/api/search_customer")
def search_customer():
    q = request.args.get("q", "").lower()
    results = []

    if q:
        customers = Customer.query.filter(
            or_(
                Customer.mkhang.ilike(f"%{q}%"),
                Customer.buyer.ilike(f"%{q}%"),
                Customer.company.ilike(f"%{q}%"),
                Customer.tax.ilike(f"%{q}%")
            )
        ).limit(50).all()

        for c in customers:
            results.append({
                "value": c.mkhang,
                "label": f"{c.mkhang} - {c.company} - {c.tax}",
                "ho_ten": c.buyer,
                "ten": c.company,
                "mst": c.tax,
                "dchi": c.address
            })

    return jsonify(results)

#------------------ Gọi trang cấu hình hóa đơn ------------------
from flask import render_template

@app.route('/config_invoice')
def serve_config_invoice():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template('config_invoice.html')
#------------------ Gọi file config.json cấu hình hóa đơn ------------------
import os
import json
from flask import request, jsonify

CONFIG_PATH = os.path.join(os.path.dirname(__file__), "config", "config.json")

@app.route('/load_config_invoice', methods=['GET'])
def load_config_invoice():
    try:
        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            config = json.load(f)
        return jsonify(config)
    except Exception as e:
        return jsonify({"error": str(e), "message": "Không đọc được cấu hình"}), 500

@app.route('/save_config_invoice', methods=['POST'])
def save_config_invoice():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "Không có dữ liệu gửi lên"}), 400

        # Đọc cấu hình cũ nếu có
        if os.path.exists(CONFIG_PATH):
            with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                try:
                    existing_config = json.load(f)
                except:
                    existing_config = {}
        else:
            existing_config = {}

        provider = data.get("provider")
        if provider:
            existing_config["provider"] = provider
            existing_config[provider] = data.get(provider, {})

        with open(CONFIG_PATH, "w", encoding="utf-8") as f:
            json.dump(existing_config, f, indent=2, ensure_ascii=False)

        return jsonify({"success": True, "message": "✅ Đã lưu cấu hình thành công."})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


#------------------ Gọi file fuel.json gán tên nhiên liệu ------------------
def map_fuel_name(code):
    fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
    try:
        with open(fuel_path, "r", encoding="utf-8") as f:
            fuel_map = json.load(f)
        return fuel_map.get(code, code)
    except:
        return code

@app.route("/fuel_map")
def get_fuel_map():
    fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
    try:
        with open(fuel_path, "r", encoding="utf-8") as f:
            return jsonify(json.load(f))
    except:
        return jsonify({})
        
#------------------ Save file fuel.json gán tên nhiên liệu setting ------------------
@app.route("/setting/fuel", methods=["GET"])
def get_fuel_config():
    fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
    with open(fuel_path, "r", encoding="utf-8") as f:
        return jsonify(json.load(f))    

@app.route("/setting/fuel", methods=["POST"])
def save_fuel_config():
    fuel_data = request.json
    fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
    with open(fuel_path, "w", encoding="utf-8") as f:
        json.dump(fuel_data, f, ensure_ascii=False, indent=2)
    return jsonify({"success": True})

#------------------ Gọi file tank.json ánh xạ bồn nhiên liệu ------------------
def map_tank_name(hose):
    tank_path = os.path.join(os.path.dirname(__file__), "config", "tank.json")
    try:
        with open(tank_path, "r", encoding="utf-8") as f:
            tank_map = json.load(f)
        return tank_map.get(hose, "")
    except:
        return ""

@app.route("/tank_map")
def get_tank_map():
    tank_path = os.path.join(os.path.dirname(__file__), "config", "tank.json")
    try:
        with open(tank_path, "r", encoding="utf-8") as f:
            return jsonify(json.load(f))
    except:
        return jsonify({})

#------------------ API trả danh sách pumpSerial đang dùng thực tế ------------------
@app.route("/api/tank_map")
def api_tank_map():
    try:
        # Lấy danh sách serial đang dùng
        serials = db.session.query(Log.pumpSerial).distinct().all()
        serials = [str(s[0]) for s in serials if s[0] is not None]

        # Đọc map tank hiện tại
        tank_path = os.path.join("config", "tank.json")
        tank_map = {}
        if os.path.exists(tank_path):
            with open(tank_path, "r", encoding="utf-8") as f:
                tank_map = json.load(f)

        # Tạo danh sách kết quả
        result = []
        for serial in sorted(serials):
            result.append({
                "pumpSerial": serial,
                "tank": tank_map.get(serial, "")
            })

        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})

#------------------ Save file tank.json từ setting ------------------
@app.route("/setting/tank", methods=["GET"])
def get_tank_config():
    tank_path = os.path.join(os.path.dirname(__file__), "config", "tank.json")
    with open(tank_path, "r", encoding="utf-8") as f:
        return jsonify(json.load(f))

@app.route("/setting/tank", methods=["POST"])
def save_tank_config():
    tank_data = request.json
    tank_path = os.path.join(os.path.dirname(__file__), "config", "tank.json")
    with open(tank_path, "w", encoding="utf-8") as f:
        json.dump(tank_data, f, ensure_ascii=False, indent=2)
    return jsonify({"success": True})


#------------------Tìm log mới, nếu có thì mới cập nhật load lại trang, giảm tải trang web------------------
@app.route("/api/latest_log_code")
def latest_log_code():
    latest = db.session.query(Log.transactionCode).order_by(Log.id.desc()).first()
    return jsonify({"code": latest.transactionCode if latest else ""})

#------------------ check_invoice: truy cập trang quản lý log ------------------    
@app.route("/check_invoice")
def check_invoice_page():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template("check_invoice.html")

@app.route("/check_invoice_mobile")
def check_invoice_mobile_page():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template("check_invoice_mobile.html")

#------------------ check_invoice: Xử lý các log đã xuất hóa đơn lỗi và cho xuất lại ------------------        
from sqlalchemy import text

@app.route('/api/check_invoice', methods=['POST'])
def check_invoice():
    data = request.get_json()
    code = data.get("transactionCode")

    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy log")

    # Xóa hóa đơn
    log.isInvoiced = False
    log.invoiceNumber = None
    log.verificationCode = None
    log.provider = None
    log.invoiceId = None
    log.internalNote = None

    # ✅ Xóa KH trong bảng `invoice_log` (chính xác theo Excel)
    db.session.execute(
        text("UPDATE invoice_log SET custom_data = NULL WHERE transaction_code = :code"),
        {"code": code}
    )

    db.session.commit()
    return jsonify(success=True)

#------------------ Xử lý các log đã xuất hóa đơn có số hđ nhưng db không có ------------------    
@app.route("/api/assign_invoice_number", methods=["POST"])
def assign_invoice_number():
    data = request.json
    code = data.get("transactionCode")
    invoice = data.get("invoiceNumber")

    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy log")

    log.invoiceNumber = invoice
    log.isInvoiced = True
    db.session.commit()

    return jsonify(success=True)

#------------------ Xử lý trang tài khoản ngân hàng ------------------   
@app.route("/bank_setting")
def bank_setting_page():
    if session.get("role") != "admin":
        return "❌ Bạn không có quyền truy cập", 403
    return render_template("bank_setting.html")

#------------------ Gọi bank.json tài khoản ngân hàng ----------------
@app.route("/api/bank", methods=["GET"])
def get_bank_setting():
    import os, json
    path = os.path.join("config", "bank.json")
    if os.path.exists(path):
        with open(path, "r", encoding="utf-8") as f:
            return jsonify(json.load(f))
    return jsonify({})

@app.route("/api/bank", methods=["POST"])
def save_bank_setting():
    import os, json
    data = request.json
    path = os.path.join("config", "bank.json")
    with open(path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    return jsonify(success=True)

#------------------ Gọi trạng thái nút QR đã chuyển khoản hay chưa ----------------
@app.route("/api/mark_qr_paid", methods=["POST"])
def mark_qr_paid():
    data = request.get_json()
    code = data.get("transactionCode")
    log = Log.query.filter_by(transactionCode=code).first()
    if not log:
        return jsonify({"success": False, "message": "Không tìm thấy giao dịch"})
    
    log.qr_paid = True
    db.session.commit()
    return jsonify({"success": True})

#------------------ Xử lý xóa đã chuyển khoản tại check_log ----------------
@app.route("/api/unmark_qr_paid", methods=["POST"])
def unmark_qr_paid():
    data = request.get_json()
    code = data.get("transactionCode")
    if not code:
        return jsonify(success=False, message="Thiếu mã giao dịch.")

    log = db.session.query(Log).filter_by(transactionCode=code).first()
    if not log:
        return jsonify(success=False, message="Không tìm thấy giao dịch.")

    log.qr_paid = False
    db.session.commit()
    return jsonify(success=True)
   
#------------------Chọn trạm------------------
@app.route('/stations', methods=['GET'])
def get_stations():
    stations = Station.query.all()
    result = [{"id": s.id, "code": s.stationCode, "name": s.stationName} for s in stations]
    return jsonify(result)

#------------------ API debug để kiểm tra session ------------------
@app.route("/api/debug_session")
def debug_session():
    if session.get("role") != "admin":
        return jsonify({"error": "Không có quyền truy cập"}), 403

    return jsonify({
        "username": session.get("username"),
        "role": session.get("role"),
        "api_account": session.get("api_account"),
        "api_pass": session.get("api_pass"),
        "session_keys": list(session.keys())
    })

#------------------ API debug để kiểm tra user trong DB ------------------
@app.route("/api/debug_user/<username>")
def debug_user(username):
    if session.get("role") != "admin":
        return jsonify({"error": "Không có quyền truy cập"}), 403

    user = User.query.filter_by(username=username).first()
    if not user:
        return jsonify({"error": "Không tìm thấy user"}), 404

    return jsonify({
        "id": user.id,
        "username": user.username,
        "role": user.role,
        "api_account": user.api_account,
        "api_pass": user.api_pass
    })

#------------------ API để refresh thông tin API credentials trong session ------------------
@app.route("/api/refresh_api_credentials", methods=["POST"])
def refresh_api_credentials():
    username = session.get("username")
    if not username:
        return jsonify({"error": "Chưa đăng nhập"}), 401

    # Lấy thông tin user mới nhất từ database
    user = User.query.filter_by(username=username).first()
    if not user:
        return jsonify({"error": "Không tìm thấy user"}), 404

    # Cập nhật session với thông tin mới nhất
    if user.api_account and user.api_pass:
        session['api_account'] = user.api_account
        session['api_pass'] = user.api_pass
        logger.info(f"✅ ĐÃ REFRESH SESSION: user={username}, api_account={user.api_account}")
        return jsonify({
            "success": True,
            "message": f"Đã cập nhật thông tin API cho user {username}",
            "api_account": user.api_account,
            "has_credentials": True
        })
    else:
        session.pop('api_account', None)
        session.pop('api_pass', None)
        logger.info(f"⚠️ REFRESH SESSION: user={username} không có API credentials")
        return jsonify({
            "success": True,
            "message": f"User {username} không có thông tin API riêng, sẽ dùng config mặc định",
            "api_account": None,
            "has_credentials": False
        })

#------------------ Gọi trang cài đặt user user_manage ------------------
@app.route("/user_manage")
def user_manage():
    if session.get("role") != "admin":
        return "Bạn không có quyền truy cập", 403
    # Ẩn 'montech'
    users = User.query.filter(User.username != 'montech').all()
    return render_template("user_manage.html", users=users)

#------------------ Xử lý tạo user và Không cho tạo quá 2 user------------------   
@app.route("/create_user", methods=["POST"])
def create_user():
    if session.get("role") != "admin":
        return "Bạn không có quyền tạo tài khoản", 403

    # Đếm số lượng user hiện có
    total_users = User.query.count()
    if total_users >= 5:
        return "⚠️ Tối đa 5 tài khoản người dùng. Không thể tạo thêm.", 400

    data = request.form
    if User.query.filter_by(username=data["username"]).first():
        return "⚠️ Tài khoản đã tồn tại"

    # Gán luôn api_account và api_pass nếu có
    new_user = User(
        username=data["username"],
        role=data["role"],
        api_account=data.get("api_account"),
        api_pass=data.get("api_pass")
    )
    new_user.set_password(data["password"])

    db.session.add(new_user)
    db.session.commit()
    return jsonify({"success": True, "message": "✅ Đã tạo tài khoản thành công."})
   
#------------------ Xử lý xóa user ------------------  
@app.route("/delete_user", methods=["POST"])
def delete_user():
    if session.get("role") != "admin":
        return "Bạn không có quyền", 403

    username = request.form.get("username")
    if username in ["admin", "montech"]:
        return "Không được xóa tài khoản quan trọng"

    user = User.query.filter_by(username=username).first()
    if user:
        db.session.delete(user)
        db.session.commit()
    return jsonify({"success": True, "message": f"✅ Đã xóa tài khoản {username}."})

#------------------ Xử lý đổi mật khẩu đăng nhập ------------------
@app.route("/change_password", methods=["POST"])
def change_password():
    if session.get("role") != "admin":
        return "Bạn không có quyền", 403

    username = request.form.get("username")
    new_password = request.form.get("new_password")

    if username == "admin":
        return "Không được đổi mật khẩu tài khoản admin"

    user = User.query.filter_by(username=username).first()
    if not user:
        return "Không tìm thấy tài khoản"

    user.set_password(new_password)
    db.session.commit()
    return jsonify({"success": True, "message": f"✅ Đã đổi mật khẩu cho {username}."})

#------------------ Xử lý cập nhật API credentials ------------------
@app.route("/update_api_credentials", methods=["POST"])
def update_api_credentials():
    if session.get("role") != "admin":
        return "Bạn không có quyền", 403

    username = request.form.get("username")
    api_account = request.form.get("api_account", "").strip()
    api_pass = request.form.get("api_pass", "").strip()

    user = User.query.filter_by(username=username).first()
    if not user:
        return "Không tìm thấy tài khoản"

    # Cập nhật thông tin API
    user.api_account = api_account if api_account else None
    user.api_pass = api_pass if api_pass else None
    db.session.commit()

    # Nếu đang cập nhật cho user hiện tại, refresh session
    if username == session.get("username"):
        if user.api_account and user.api_pass:
            session['api_account'] = user.api_account
            session['api_pass'] = user.api_pass
            logger.info(f"✅ ĐÃ CẬP NHẬT SESSION: user={username}, api_account={user.api_account}")
        else:
            session.pop('api_account', None)
            session.pop('api_pass', None)
            logger.info(f"⚠️ ĐÃ XÓA API CREDENTIALS KHỎI SESSION: user={username}")

    return jsonify({"success": True, "message": f"✅ Đã cập nhật thông tin API cho {username}."})
#------------------ Xử lý đăng nhập bằng Redis ------------------ 
from flask_caching import Cache

cache = Cache(app, config={
    'CACHE_TYPE': 'redis',
    'CACHE_REDIS_URL': 'redis://127.0.0.1:6379/0'  # Redis local Windows
})
ACTIVE_USERS_KEY = f"active_logged_users_{APP_ID}"

from uuid import uuid4

@app.before_request
def before_every_request():
    allowed = ['login', 'logout', 'static', 'receive_log', 'invoice_lock', 'import_log']
    ep = request.endpoint or ''
    if not session.get('username') and not any(x in ep for x in allowed):
        return redirect('/login')

    username = session.get('username')
    role = session.get('role')
    
    if username:
        # Tạo session ID nếu chưa có
        if 'sid' not in session:
            session['sid'] = str(uuid4())

        sid = session['sid']
        now = time.time()
        key = f"{username}_{sid}"

        # Đọc cache đang online
        active_users = cache.get(ACTIVE_USERS_KEY) or {}

        # Cập nhật người dùng hiện tại
        active_users[key] = now

        # Lọc lại cache: chỉ giữ người hoạt động trong 60s
        active_users = {
            k: t for k, t in active_users.items()
            if now - t < 60
        }

        # Lưu lại
        cache.set(ACTIVE_USERS_KEY, active_users)

@app.route('/active_users')
def get_active_logged_users():
    active_users = cache.get(ACTIVE_USERS_KEY) or {}
    return jsonify({"active": len(active_users)})

#------------------ Xử lý đăng nhập đăng xuất ------------------
from flask import session, redirect, url_for

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.form
        user = User.query.filter_by(username=data['username']).first()
        if user and user.check_password(data['password']):
            
            # Kiểm tra trùng đăng nhập nếu KHÔNG phải tài khoản banhang
            if user.username != "banhang":
                active_users = cache.get(ACTIVE_USERS_KEY) or {}
                now = time.time()
                for k, t in active_users.items():
                    uname = k.split("_")[0]
                    if uname == user.username and now - t < 30:
                        return render_template("login.html", error=f"❌ Tài khoản [{user.username}] đang đăng nhập từ thiết bị khác.")

            # ✅ Đăng nhập thành công → lưu thông tin vào session
            session['username'] = user.username
            session['role'] = user.role

            # ✅ Nếu user có API riêng → lưu vào session để override VNPT Account
            logger.info(f"🔍 DEBUG LOGIN: user={user.username}, api_account={user.api_account}, api_pass={user.api_pass}")

            if user.api_account and user.api_pass:
                session['api_account'] = user.api_account
                session['api_pass'] = user.api_pass
                logger.info(f"✅ ĐÃ LƯU VÀO SESSION: api_account={user.api_account}")
            else:
                session.pop('api_account', None)
                session.pop('api_pass', None)
                logger.info(f"⚠️ KHÔNG CÓ API CREDENTIALS: Sử dụng config mặc định")

            return redirect(url_for('index'))
    return render_template("login.html")

@app.route('/logout')
def logout():
    username = session.get("username")
    sid = session.get("sid")

    if username and sid:
        key = f"{username}_{sid}"
        active_users = cache.get(ACTIVE_USERS_KEY) or {}
        if key in active_users:
            del active_users[key]
            cache.set(ACTIVE_USERS_KEY, active_users)

    session.clear()
    return redirect(url_for('login'))
       
#------------------ Xử lý bấm nút backup database auto_backup.py ------------------ 
@app.route("/api/manual-backup")
def manual_backup():
    try:
        from auto_backup import run_backup_once
        result = run_backup_once()
        return jsonify(success=True, message=result)
    except Exception as e:
        return jsonify(success=False, message=str(e))

#------------------ Xử lý đọc tiền thành chữ ở updatepreview ------------------ 
@app.route("/api/num2words", methods=["POST"])
def convert_num2words():
    from num2words import num2words
    data = request.get_json()
    amount = int(data.get("amount", 0))
    chu = num2words(amount, lang="vi").replace("lẻ", "không trăm")
    chu = chu[0].upper() + chu[1:] + " đồng chẵn."
    return jsonify({"chu": chu})


#------------------ Xử lý duyệt file thư mục debug ------------------ 
@app.route("/debug")
def serve_debug_browser():
    if session.get("role") != "admin" or session.get("username") != "montech":
        return "Trang không tồn tại", 403
    return render_template("debug.html")


@app.route("/api/debug_folders")
def api_debug_folders():
    path = os.path.join(os.getcwd(), "debug")
    folders = sorted([f for f in os.listdir(path) if os.path.isdir(os.path.join(path, f))], reverse=True)
    return jsonify({"folders": folders})


@app.route("/api/debug_files")
def api_debug_files():
    base_path = os.path.join(os.getcwd(), "debug")
    try:
        results = []
        for folder in sorted(os.listdir(base_path), reverse=True):
            folder_path = os.path.join(base_path, folder)
            if not os.path.isdir(folder_path): continue

            for name in os.listdir(folder_path):
                full_path = os.path.join(folder_path, name)
                if os.path.isfile(full_path):
                    size_kb = os.path.getsize(full_path) / 1024
                    created_at = datetime.fromtimestamp(os.path.getctime(full_path)).strftime('%d/%m/%Y %H:%M')

                    results.append({
                        "folder": folder,
                        "name": name,
                        "size": f"{size_kb:.1f} KB",
                        "created_at": created_at
                    })
        return jsonify({"files": results})
    except Exception as e:
        return jsonify({"files": [], "error": str(e)})


@app.route("/debug_file/<folder>/<path:filename>")
def serve_debug_file(folder, filename):
    from flask import send_file
    import os

    debug_dir = os.path.join("debug", folder)
    full_path = os.path.join(debug_dir, filename)

    # Nếu file không tồn tại và filename không có ".txt" → dò file theo prefix
    if not os.path.exists(full_path) and not filename.endswith(".txt"):
        try:
            for fname in os.listdir(debug_dir):
                if fname.startswith(f"{filename}_") and fname.endswith(".txt"):
                    full_path = os.path.join(debug_dir, fname)
                    break
            else:
                return f"Không tìm thấy file log bắt đầu bằng {filename}_", 404
        except Exception as e:
            logger.exception(f"❌ Lỗi khi dò file log từ số hóa đơn: {filename}")
            return f"Lỗi khi dò file: {e}", 500

    logger.info(f"📂 Đang mở file log: {full_path}")
    try:
        return send_file(full_path, mimetype="text/plain")
    except Exception as e:
        logger.info(f"❌ Lỗi khi mở file debug: {full_path}")
        return f"Lỗi khi mở file: {e}", 500

#------------------ check_log: Xử lý các log bị thiếu ------------------    
@app.route("/check_log")
def check_log_page():
    if session.get("role") != "admin" or session.get("username") != "montech":
        return "Trang không tồn tại", 403
    return render_template("check_log.html")

from sqlalchemy import func

@app.route("/api/check_missing_logs")
def check_missing_logs():
    from collections import defaultdict
    from datetime import datetime

    start_date = request.args.get("start")
    end_date = request.args.get("end")

    if not start_date or not end_date:
        return jsonify({"success": False, "message": "Thiếu ngày bắt đầu hoặc kết thúc"}), 400

    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError:
        return jsonify({"success": False, "message": "Định dạng ngày không hợp lệ"}), 400

    logs = Log.query.filter(Log.transactionDate >= start_date, Log.transactionDate <= end_date).all()
    grouped = defaultdict(list)

    faucet_map = {}  # (serial, date) → faucetNo (lấy từ log đầu tiên khớp)

    for log in logs:
        try:
            parts = log.transactionCode.split("_")
            if len(parts) != 4:
                continue
            pump_no = int(parts[-1])
            key = (log.pumpSerial, log.transactionDate)
            grouped[key].append(pump_no)

            # Ghi nhớ faucetNo để hiển thị "Cò X"
            if key not in faucet_map:
                faucet_map[key] = log.faucetNo
        except:
            continue

    result = []
    for (serial, date), pump_nos in grouped.items():
        pump_nos = sorted(set(pump_nos))
        expected = list(range(min(pump_nos), max(pump_nos) + 1))
        missing = list(sorted(set(expected) - set(pump_nos)))
        if missing:
            result.append({
                "serial": serial,
                "date": date,
                "faucet": faucet_map.get((serial, date), None),
                "missing": missing,
                "total_missing": len(missing)
            })

    return jsonify({"success": True, "data": result})
    
#------------------ check_log: Xử lý xóa log theo ngày ------------------     
@app.route("/api/delete_logs_by_date", methods=["POST"])
def delete_logs_by_date():
    username = session.get("username")
    if username != "montech":
        return jsonify(success=False, message="❌ Chỉ tài khoản [montech] mới được phép xóa"), 403

    try:
        data = request.get_json()
        date_str = data.get("date")
        password = data.get("password", "")
        delete_invoice = data.get("deleteInvoice", False)

        if not date_str or not password:
            return jsonify(success=False, message="Thiếu ngày hoặc mật khẩu."), 400

        user = User.query.filter_by(username="montech").first()
        if not user or not user.check_password(password):
            return jsonify(success=False, message="❌ Sai mật khẩu xác nhận."), 403

        # Lấy danh sách transactionCode cần xóa
        codes = [r[0] for r in db.session.query(Log.transactionCode).filter_by(transactionDate=date_str).all()]

        # Nếu yêu cầu xóa cả invoice_log
        if delete_invoice and codes:
            db.session.execute(
                text("DELETE FROM invoice_log WHERE transaction_code IN :codes"),
                {"codes": tuple(codes)}
            )

        # Xóa log theo ngày
        db.session.query(Log).filter_by(transactionDate=date_str).delete()

        db.session.commit()
        return jsonify(success=True, message=f"✅ Đã xóa toàn bộ giao dịch ngày {date_str}.")

    except Exception as e:
        db.session.rollback()
        return jsonify(success=False, message=f"❌ Lỗi hệ thống: {str(e)}")

#------------------ check_log: Xử lý hiển thị IP WAN ---------------------
@app.route("/api/latest_ipwan")
def get_latest_ipwan():
    return jsonify({
        "ip": latest_ipwan["ip"],
        "time": latest_ipwan["time"]
    })

#------------------ check_log: So sánh tải csv từ IP WAN với DB đã lưu ---------------------
@app.route("/api/compare_log", methods=["GET"])
def compare_log_with_monbox():
    from datetime import datetime
    import requests, csv
    from io import StringIO

    date_str = request.args.get("date")
    ip = request.args.get("ip")  # Lấy IP từ frontend
    if not date_str:
        return jsonify({"success": False, "message": "Thiếu ngày"}), 400

    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    except:
        return jsonify({"success": False, "message": "Sai định dạng ngày"}), 400

    year = date_obj.strftime("%Y")
    month = date_obj.strftime("%m")
    filename = date_obj.strftime("%Y%m%d")

    url = f"http://{ip}/edit_sdfs?download=/{year}/{month}/{filename}.csv"

    logger.info(f"📤 So sánh log - Gọi URL: {url}")
    response = requests.get(url, auth=MONBOX_AUTH)
    logger.info(f"📥 Trạng thái response: {response.status_code}")

    if response.status_code != 200:
        return jsonify({"success": False, "message": f"Lỗi tải file: {response.status_code}"}), 500

    content = response.content.decode("utf-8-sig")
    reader = csv.DictReader(StringIO(content))

    # Lấy log DB trong ngày
    db_logs = Log.query.filter_by(transactionDate=date_str).all()
    db_codes = set(log.transactionCode for log in db_logs)

    csv_codes = set()
    for row in reader:
        try:
            serial = str(row.get("SerialNo", "")).strip()
            date_raw = str(row.get("Date", "")).strip().replace("/", "-")
            time_raw = str(row.get("Time", "00:00:00")).strip()
            pump_no = str(row.get("PumpNo", "0")).strip()

            date_obj = datetime.strptime(date_raw, "%Y-%m-%d")
            date_for_code = date_obj.strftime("%d%m%y")
            time_for_code = time_raw.replace(":", "")
            code = f"{serial}_{date_for_code}_{time_for_code}_{pump_no}"

            csv_codes.add(code)
        except:
            continue

    missing_in_db = sorted(list(csv_codes - db_codes))
    missing_in_csv = sorted(list(db_codes - csv_codes))

    return jsonify({
        "success": True,
        "total_csv": len(csv_codes),
        "total_db": len(db_codes),
        "missing_in_db": missing_in_db,
        "missing_in_csv": missing_in_csv
    })

#------------------ Xử lý tải file csv theo ngày từ trang check_log ------------------ 
def import_csv_from_text(csv_text):
    from datetime import datetime
    from io import StringIO
    import csv

    reader = csv.DictReader(StringIO(csv_text))
    inserted, skipped = 0, 0
    station = Station.query.filter_by(stationCode=STORE_CODE).first()

    for row in reader:
        try:
            serial = str(row.get("SerialNo", "")).strip()
            date_raw = str(row.get("Date", "")).strip().replace("/", "-")
            time_raw = str(row.get("Time", "00:00:00")).strip()
            pump_no = str(row.get("PumpNo", "0")).strip()
            pump_id = str(row.get("PumpId", "0")).strip()

            date_obj = datetime.strptime(date_raw, "%Y-%m-%d")
            date_str_db = date_obj.strftime("%Y-%m-%d")
            date_for_code = date_obj.strftime("%d%m%y")
            time_for_code = time_raw.replace(":", "")
            code = f"{serial}_{date_for_code}_{time_for_code}_{pump_no}"

            if Log.query.filter_by(transactionCode=code).first():
                skipped += 1
                continue

            log_entry = Log(
                station_id=station.id,
                storeCode=STORE_CODE,
                dataType=2,
                verificationCode="AUTO",
                transactionCode=code,
                partnerCode="P01",
                companyCode="C01",
                hardwareId="MONBOX",
                faucetNo=int(pump_id),
                pumpSerial=int(serial),
                fuelType=row.get("Fuel", "N/A").strip(),
                transactionDate=date_str_db,
                transactionTime=time_raw,
                transactionCost=int(row.get("Money", 0)),
                transactionAmount=int(row.get("Liter", 0)),
                transactionPrice=int(row.get("Price", 0)),
            )
            db.session.add(log_entry)
            inserted += 1
        except Exception as e:
            logger.info(f"❌ Lỗi khi import dòng: {row} — {e}")
            skipped += 1

    db.session.commit()
    return inserted, skipped

#----- Xử lý nhận log từ checkbox để gửi auto_scheduler chạy mode_3, thay thế sendBatchInvoices_xxx -------------- 
@app.route('/api/send_selected_logs', methods=['POST'])
def send_selected_logs():
    try:
        data = request.get_json()
        codes = data.get("codes", [])

        if not codes or not isinstance(codes, list):
            return jsonify({"success": False, "message": "Thiếu danh sách mã giao dịch"}), 400

        from auto_scheduler import run_mode_3, load_setting, build_default_buyer
        setting = load_setting()
        buyer_info = build_default_buyer(setting)

        def run_with_context():
            with app.app_context():
                run_mode_3(setting, buyer_info, codes)

        threading.Thread(target=run_with_context, daemon=True).start()

        return jsonify({"success": True, "message": f"Đang gửi {len(codes)} hóa đơn qua backend (CHẾ ĐỘ 3)..."}), 200

    except Exception as e:
        import traceback
        logger.info(f"❌ Lỗi tại /api/send_selected_logs: {e}")
        logger.info(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)})

#------- API trả số lượng đã gửi / tổng số log trong quá trình xuất hóa đơn -----------------------------------------------
@app.route("/api/invoice-stats")
def get_invoice_stats():
    try:
        from auto_scheduler import log_sent_count, log_total_count
        return jsonify({
            "sent": log_sent_count,
            "total": log_total_count
        })
    except Exception as e:
        return jsonify({"sent": 0, "total": 0})

#------- API trả dung lượng tổng database đang có  -----------------------------------------------
from sqlalchemy import text  # đặt ở đầu file app.py

@app.route("/api/database_size")
def get_database_size():
    try:
        sql = text("""
            SELECT table_schema AS db, 
                   ROUND(SUM(data_length + index_length) / 1024, 2) AS total_kb
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            GROUP BY table_schema
        """)
        result = db.session.execute(sql).fetchone()
        return jsonify(success=True, size_kb=result.total_kb if result else 0)
    except Exception as e:
        return jsonify(success=False, message=str(e))



# ------------------------------------------------------------------------
from flask import render_template
if __name__ == '__main__':
    logger.info(f"🚀 Flask app sử dụng Port:{APP_PORT} và URL:{MONBOX_URL}")
    with app.app_context():
        db.create_all()

        # Tạo trạm mẫu (nếu chưa có)
        if not Station.query.first():
            db.session.add_all([
                Station(stationCode='S01', stationName='CHXD 01'),
                Station(stationCode='S02', stationName='CHXD 02'),
                Station(stationCode='S03', stationName='CHXD 03'),
                Station(stationCode='S04', stationName='CHXD 04'),
                Station(stationCode='S05', stationName='CHXD 05'),
                Station(stationCode='S06', stationName='CHXD 06'),
            ])
            db.session.commit()
            logger.info("✅ Đã tạo bảng Station và thêm dữ liệu mẫu")

        # Tạo 2 tài khoản admin và 1 user banhang mặc định nếu chưa có
        default_users = [
            {"username": "admin", "password": "adminadmin", "role": "admin"},
            {"username": "montech", "password": "Iphone@672910", "role": "admin"},
            {"username": "banhang", "password": "123456", "role": "user"},
        ]

        for item in default_users:
            if not User.query.filter_by(username=item["username"]).first():
                user = User(username=item["username"], role=item["role"])
                user.set_password(item["password"])
                db.session.add(user)
                logger.info(f"✅ Đã tạo tài khoản [{item['username']}] với quyền [{item['role']}]")

        db.session.commit()

        # Tự động Backup database
        from auto_backup import schedule_auto_backup
        schedule_auto_backup()
    
    app.run(host='0.0.0.0', port=APP_PORT, debug=True, use_reloader=True)




