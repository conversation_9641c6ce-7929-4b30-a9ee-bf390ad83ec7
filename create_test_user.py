#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để tạo user test với API credentials
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, User
    
    print("🔍 Tạo user test với API credentials...")
    
    with app.app_context():
        # Kiểm tra xem user ketoan đã tồn tại chưa
        existing_user = User.query.filter_by(username='ketoan').first()
        
        if existing_user:
            print(f"✅ User 'ketoan' đã tồn tại")
            print(f"   Current API Account: {existing_user.api_account}")
            print(f"   Current API Pass: {existing_user.api_pass}")
            
            # Cập nhật thông tin API
            existing_user.api_account = "montech1"
            existing_user.api_pass = "Einv@oi@vn#pt25"
            db.session.commit()
            print(f"✅ Đã cập nhật API credentials cho user 'ketoan'")
            
        else:
            # Tạo user mới
            new_user = User(
                username='ketoan',
                role='admin',
                api_account='montech1',
                api_pass='Einv@oi@vn#pt25'
            )
            new_user.set_password('123456')  # Mật khẩu mặc định
            
            db.session.add(new_user)
            db.session.commit()
            print(f"✅ Đã tạo user 'ketoan' với API credentials")
        
        # Kiểm tra lại
        user = User.query.filter_by(username='ketoan').first()
        print(f"\n📋 Thông tin user 'ketoan':")
        print(f"   ID: {user.id}")
        print(f"   Username: {user.username}")
        print(f"   Role: {user.role}")
        print(f"   API Account: {user.api_account}")
        print(f"   API Pass: {user.api_pass}")
        print(f"   Has API credentials: {bool(user.api_account and user.api_pass)}")

except Exception as e:
    print(f"❌ Lỗi: {e}")
    import traceback
    traceback.print_exc()
