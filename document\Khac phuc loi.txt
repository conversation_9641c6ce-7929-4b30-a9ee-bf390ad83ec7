1.	<PERSON><PERSON><PERSON>, chon Path
2.	<PERSON><PERSON><PERSON> (chạy 2, 3 lần OK)
3.	<PERSON><PERSON><PERSON> nginx (check chiếm port trước, chạy check_port_80_for_nginx.bat để stop các service system chiếm port 80). Kiểm tra port, domain conf trong nginx.
Chạy lệnh trong Powershell để tắt:

Stop-Service W3SVC -Force
Stop-Service WinRM -Force
Stop-Service HTTP -Force
Stop-Service SSDPSRV -Force
Stop-Service iphlpsvc -Force

cmd /c "sc config W3SVC start= disabled"
cmd /c "sc config WinRM start= disabled"
cmd /c "sc config HTTP start= disabled"
cmd /c "sc config SSDPSRV start= disabled"
cmd /c "sc config iphlpsvc start= disabled"


4.	Cài MySQL (chọn Server Only)
5.	Chuyển db máy khác qua thì khắc phục import sau đây:

'mysql' is not recognized as an internal or external command,
operable program or batch file.

✅ Nguyên nhân:
<PERSON><PERSON><PERSON> mysql không được nhận diện vì chưa thêm đường dẫn MySQL vào biến môi trường PATH của hệ thống.

✅ Cách khắc phục import:
🔧 Bước 1: Tìm đường dẫn đến MySQL
Giả sử bạn cài MySQL theo mặc định thì đường dẫn thường là:

C:\Program Files\MySQL\MySQL Server 8.0\bin;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\

🔧 Bước 2: Thêm vào biến môi trường
Bấm Win + R → nhập: sysdm.cpl → Enter.

Chuyển sang tab Advanced → bấm Environment Variables.

Trong mục System variables, chọn dòng Path → bấm Edit.

Bấm New và dán đường dẫn đến thư mục bin của MySQL, ví dụ:

C:\Program Files\MySQL\MySQL Server 8.0\bin
Bấm OK liên tục để lưu.

🔁 Bước 3: Thử lại
Mở cửa sổ Command Prompt mới (sau khi thêm PATH) và chạy lại đoạn import.

Ví dụ lệnh trong batch script là:

mysql -u root -p your_password thuanhoa < thuanhoa_backup.sql
Nếu bạn dùng .bat, hãy đảm bảo mysql.exe gọi đúng từ dòng lệnh.

✅ Kiểm tra nhanh:
Sau khi thêm PATH, mở cmd và gõ:

mysql --version
→ Nếu ra phiên bản như mysql Ver 8.0.x, bạn đã cấu hình đúng.

