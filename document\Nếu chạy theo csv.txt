====> Trong app.py bỏ hàm @app.route('/montechpos', methods=['POST']) và thay bằng:
#------------------ X<PERSON> lý tự động tải file logs csv ------------------ 
@app.route("/api/import_log", methods=["POST"])
def import_log_from_monbox():
    if app.config.get("isInvoiceProcessing"):
        return jsonify({"success": False, "message": "<PERSON><PERSON> xuất hóa đơn"}), 429

    try:
        import requests, csv
        from io import StringIO
        from datetime import datetime

        station = Station.query.filter_by(stationCode=STORE_CODE).first()
        if not station:
            return jsonify({"success": False, "message": f"Không tìm thấy stationCode = {STORE_CODE}"}), 404

        today = datetime.now().strftime("%Y%m%d")
        year, month = today[:4], today[4:6]
        url = f"{MONBOX_URL}/edit_sdfs?download=/{year}/{month}/{today}.csv"

        response = requests.get(url, auth=MONBOX_AUTH)
        if response.status_code != 200:
            return jsonify({"success": False, "message": f"Lỗi tải file: {response.status_code}"}), 500

        content = response.content.decode("utf-8-sig")
        reader = csv.DictReader(StringIO(content))

        inserted, skipped = 0, 0
        for row in reader:
            serial = str(row.get("SerialNo", "")).strip()
            date_raw = str(row.get("Date", "")).strip().replace("/", "-")
            time_raw = str(row.get("Time", "00:00:00")).strip()
            pump_no = str(row.get("PumpNo", "0")).strip()    # dùng trong transactionCode
            pump_id = str(row.get("PumpId", "0")).strip()    # dùng cho faucetNo

            # Parse ngày về object
            try:
                date_obj = datetime.strptime(date_raw, "%Y-%m-%d")
            except:
                try:
                    date_obj = datetime.strptime(date_raw, "%d-%m-%Y")
                except:
                    date_obj = None

            if not date_obj:
                skipped += 1
                continue

            date_str = date_obj.strftime("%Y-%m-%d")          # lưu vào DB
            date_for_code = date_obj.strftime("%d%m%y")       # dùng cho transactionCode

            time_for_code = time_raw.replace(":", "")         # HHMMSS

            code = f"{serial}_{date_for_code}_{time_for_code}_{pump_no}"

            if Log.query.filter_by(transactionCode=code).first():
                skipped += 1
                continue

            log_entry = Log(
                station_id=station.id,
                storeCode=STORE_CODE,
                dataType=2,
                verificationCode="AUTO",
                transactionCode=code,
                partnerCode="P01",
                companyCode="C01",
                hardwareId="MONBOX",
                faucetNo=int(pump_id),              # vẫn là PumpId
                pumpSerial=int(serial),
                fuelType=row.get("Fuel", "N/A").strip(),
                transactionDate=date_str,
                transactionTime=time_raw,
                transactionCost=int(row.get("Money", 0)),
                transactionAmount=int(row.get("Liter", 0)),
                transactionPrice=int(row.get("Price", 0)),
            )
            db.session.add(log_entry)
            inserted += 1

        db.session.commit()
        result = {
            "success": True,           
            "Ghi log": inserted,
            "Hiện có": skipped,
            "Tổng": inserted + skipped,
            "Trạng thái": True
        }
        logger.info(f"📥 Kết quả import: {result}")
        return jsonify(result)

    except Exception as e:
        logger.info(f"❌ Lỗi /api/import_log: {e}")
        logger.info(traceback.format_exc())
        db.session.rollback()
        return jsonify({"success": False, "message": str(e)}), 500

====> Trong main.js thêm hàm dưới và nhớ bỏ Tự động gọi fetchLogs mỗi 10 giây để không quá tải monbox
/*Tải log từ MonBox khi F5 hoặc mở trang
function importLogFromMonboxOnce() {
  const now = Date.now();
  const lastCall = localStorage.getItem("lastImportLogTime");

  if (lastCall && now - parseInt(lastCall) < 30_000) {
    console.log("⏳ Chặn gọi import log vì F5 liên tục (cách chưa đủ 30s)");
    return;
  }

  localStorage.setItem("lastImportLogTime", now);

	fetch("/api/import_log", { method: "POST" })
	  .then(async res => {
		if (!res.ok) {
		  throw new Error(`Máy chủ trả về lỗi ${res.status}`);
		}
		return res.json();
	  })
	  .then(data => {
		console.log("📥 [F5] Tải log MonBox:", data);
		if (!data.success) {
		  Swal.fire({
			icon: "error",
			html: `
			  <div style="font-size: 15px;">
				Lỗi: <b>${data.message || "Không rõ nguyên nhân"}</b><br>
				Vui lòng kiểm tra thiết bị MonBox.
			  </div>
			`,
			timer: 30000
		  });
		}
	  })
	  .catch(err => {
		console.error("❌ Lỗi tải log:", err);
		Swal.fire({
		  icon: "error",
		  html: `
			<div style="font-size: 16px;">
			  Không thể truy cập thiết bị MonBox.<br><br>
			  <b>Vui lòng thực hiện:</b>
			  <ul style="text-align:left; margin-top:5px;">
				<li>Tắt modem mạng và thiết bị MonBox</li>
				<li>Chờ 5 phút → Bật modem, rồi bật MonBox</li>
				<li>Đợi khoảng 3 phút → Tải lại trang (F5)</li>
			  </ul>
			</div>
		  `,
		  confirmButtonText: "OK",
		  confirmButtonColor: "#6c63ff",
		  width: 440,
		  timer: 30000
		});
	  });
}
