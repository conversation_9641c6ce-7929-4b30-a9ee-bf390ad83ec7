@echo off
setlocal enabledelayedexpansion
title KIEM TRA PORT 80

echo ====================================================
echo        KIEM TRA PORT 80 DANG BI CHIEM BOI AI?
echo ====================================================
echo.

REM Tim PID chiem port 80
for /f "tokens=5" %%i in ('netstat -ano ^| find ":80" ^| find "LISTENING"') do (
    set PID=%%i
)

if not defined PID (
    echo Port 80 hien dang trong. Khong co tien trinh nao chiem.
    goto END
)

echo Port 80 dang bi chiem boi tien trinh co PID: !PID!

REM Tim ten tien trinh
for /f "skip=3 tokens=1,*" %%a in ('tasklist /FI "PID eq !PID!"') do (
    set PROC=%%a
    goto FOUND
)

:FOUND
echo ➤ Tien trinh dang chiem port 80: !PROC!
echo.

REM Nguoi dung muon dung tien trinh
set /p CHOICE=Ban co muon tu dong giai phong port 80 (Y/N)? 
if /i "%CHOICE%"=="Y" (
    echo.
    if /i "!PROC!"=="System" (
        echo Dang co the la IIS hoac HTTP chiem port 80.
        echo ➤ Dang go bo HTTP Server tren port 80...
        net stop http
        sc stop W3SVC
        iisreset /stop
    ) else (
        echo ➤ Dang kill tien trinh !PROC! ...
        taskkill /PID !PID! /F
    )
) else (
    echo Ban da chon khong giai phong. Ket thuc.
)

:END
echo.
pause
