@echo off
setlocal enabledelayedexpansion

REM === Luu lai thu muc hien tai ===
set "CUR_DIR=%~dp0"

echo =========================================================================
echo MontechPOS Run (Redis + Nginx + Flask + AutoScheduler)
echo =========================================================================

REM ===================== Kiem tra Nginx =====================================
tasklist /FI "IMAGENAME eq nginx.exe" | find /I "nginx.exe" >nul
if %ERRORLEVEL%==1 (
    echo Nginx chua chay - Dang chay Nginx...
    start "" /min cmd /c "cd /d C:\nginx && nginx.exe"
) else (
    echo Nginx da chay san.
)

REM ================= Kich hoat moi truong ao va chay Flask ==================
cd /d "%CUR_DIR%"
call venv\Scripts\activate.bat

echo Dang chay Flask app.py...
start "App" cmd /k "python app.py"

REM ============ Chay auto_scheduler.py o cua so rieng de debug ==============
timeout /t 2 >nul
echo Dang chay auto_scheduler.py de tu dong xuat hoa don...
start "Auto" cmd /k "python auto_scheduler.py"

exit
