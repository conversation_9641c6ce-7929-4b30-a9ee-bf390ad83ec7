@echo off
title Chay MonTechPOS - app.py + auto_scheduler.py
cd /d "%~dp0"

set PYTHON_EXE=venv\Scripts\python.exe
set "CURRENT_DIR=%cd%"

:: === Kiem tra Python ===
if not exist "%PYTHON_EXE%" (
    echo ❌ Khong tim thay Python trong venv
    pause
    exit /b
)

:: === Kiem tra app.py ===
powershell -Command ^
    "$dir = '%CURRENT_DIR%'.ToLower();" ^
    "$found = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq 'python.exe' -and $_.CommandLine -ne $null -and $_.CommandLine.ToLower().Contains('app.py') -and $_.CommandLine.ToLower().Contains($dir) };" ^
    "if ($found) { exit 0 } else { exit 1 }"

if %errorlevel%==0 (
    echo ⚠️ app.py đang chay dung thu muc. Khong chay lai.
) else (
    echo ▶️ Dang chay app.py an...
    powershell -WindowStyle Hidden -Command ^
        "Start-Process '%PYTHON_EXE%' -ArgumentList '%CURRENT_DIR%\app.py' -WindowStyle Hidden"
)

:: === Kiem tra auto_scheduler.py ===
powershell -Command ^
    "$dir = '%CURRENT_DIR%'.ToLower();" ^
    "$found = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq 'python.exe' -and $_.CommandLine -ne $null -and $_.CommandLine.ToLower().Contains('auto_scheduler.py') -and $_.CommandLine.ToLower().Contains($dir) };" ^
    "if ($found) { exit 0 } else { exit 1 }"

if %errorlevel%==0 (
    echo ⚠️ auto_scheduler.py dang chay dung thu muc. Khong chay lai.
) else (
    echo ▶️ Dang chay auto_scheduler.py an...
    powershell -WindowStyle Hidden -Command ^
        "Start-Process '%PYTHON_EXE%' -ArgumentList '%CURRENT_DIR%\auto_scheduler.py' -WindowStyle Hidden"
)

exit
