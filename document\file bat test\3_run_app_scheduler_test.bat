@echo off
title Chay MonTechPOS - app.py + auto_scheduler.py
cd /d "%~dp0"

set PYTHON_EXE=venv\Scripts\python.exe
set "CURRENT_DIR=%cd%"
set "CURRENT_DIR_NORM=%CURRENT_DIR:\=\\%"  :: Escape regex

:: === Kiem tra Python ===
if not exist "%PYTHON_EXE%" (
    echo Khong tim thay Python trong venv
    pause
    exit /b
)

:: === Kiem tra app.py ===
powershell -Command ^
    "$dir = '%CURRENT_DIR%'.ToLower();" ^
    "$found = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq 'python.exe' -and $_.CommandLine -ne $null -and $_.CommandLine.ToLower().Contains('app.py') -and $_.CommandLine.ToLower().Contains($dir) };" ^
    "if ($found) { exit 0 } else { exit 1 }"

if %errorlevel%==0 (
    echo app.py Dang chay dung thu muc. Khong chay lai.
) else (
    echo Dang chay app.py...
    start "App" "%PYTHON_EXE%" "%CURRENT_DIR%\app.py"
)

:: === Kiem tra auto_scheduler.py ===
powershell -Command ^
    "$dir = '%CURRENT_DIR%'.ToLower();" ^
    "$found = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq 'python.exe' -and $_.CommandLine -ne $null -and $_.CommandLine.ToLower().Contains('auto_scheduler.py') -and $_.CommandLine.ToLower().Contains($dir) };" ^
    "if ($found) { exit 0 } else { exit 1 }"

if %errorlevel%==0 (
    echo auto_scheduler.py Dang chay dung thu muc. Khong chay lai.
) else (
    echo Dang chay auto_scheduler.py...
    start "Scheduler" "%PYTHON_EXE%" "%CURRENT_DIR%\auto_scheduler.py"
)

exit
