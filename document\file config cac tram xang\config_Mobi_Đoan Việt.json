{"provider": "MOBIFONE", "VNPT": {"Account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACpass": "Einv@oi@vn#pt25", "username": "satracorpservice", "password": "Einv@oi@vn#pt25", "link_api": "https://satracorp-tt78admindemo.vnpt-invoice.com.vn/PublishService.asmx", "pattern": "1/002", "serial": "C25MAT", "convert": "0", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "", "sellerName": "TRẠM XĂNG DẦU CỘNG HÒA", "sellerTaxCode": "", "sellerAddress": ""}, "MOBIFONE": {"tax_code": "**********", "username": "**********APP", "password": "DAn0T1VjgnI=", "template_code": "1C25MDV", "login_url": "https://**********.mobifoneinvoice.vn/api/Account/Login", "get_cctbao_url": "https://**********.mobifoneinvoice.vn/api/System/GetDataReferencesByRefId", "link_api": "https://**********.mobifoneinvoice.vn/api/Invoice68/SaveListHoadon78", "link_api_fkey": "http://**********.mobifoneinvoice.vn/api/Invoice68/GetHoadonFkey", "pdf_url": "https://**********.mobifoneinvoice.vn/api/Invoice68/inHoadon", "sellerFullName": "", "sellerName": "CÔNG TY TNHH THƯƠNG MẠI ĐOAN VIỆT", "sellerTaxCode": "**********", "sellerAddress": " 2/20A <PERSON><PERSON><PERSON><PERSON> 22, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Nam"}, "MOBIFONE_TEST": {"tax_code": "**********-998", "username": "55556666TEST", "password": "jdlsOUj98IQ=", "ma_dvcs": "", "login_url": "http://mobiinvoice.vn:9000/api/Account/Login", "get_cctbao_url": "", "link_api": "http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78", "generate_url": "http://mobiinvoice.vn:9000/api/Invoice68/GenerateInvoiceNumber", "cctbao_id": "d9b808ad-f9f1-4487-81a3-************", "template_code": "", "sign_mode": "", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "", "sellerName": "TRẠM XĂNG DẦU CỘNG HÒA", "sellerTaxCode": "", "sellerAddress": ""}}