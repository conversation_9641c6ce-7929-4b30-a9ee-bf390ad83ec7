{"provider": "MOBIFONE", "VNPT": {"Account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACpass": "Einv@oi@vn#pt25", "username": "satracorpservice", "password": "Einv@oi@vn#pt25", "link_api": "https://satracorp-tt78admindemo.vnpt-invoice.com.vn/PublishService.asmx", "pattern": "1/002", "serial": "C25MAT", "convert": "0", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "", "sellerName": "THUANHOA", "sellerTaxCode": "", "sellerAddress": ""}, "MOBIFONE": {"tax_code": "**********", "username": "**********", "password": "A123456", "template_code": "1C25MKS", "login_url": "http://**********.mobifoneinvoice.vn/api/Account/Login", "get_cctbao_url": "http://**********.mobifoneinvoice.vn/api/System/GetDataReferencesByRefId", "link_api": "http://**********.mobifoneinvoice.vn/api/Invoice68/SaveListHoadon78", "link_api_fkey": "http://**********.mobifoneinvoice.vn/api/Invoice68/GetHoadonFkey", "pdf_url": "http://**********.mobifoneinvoice.vn/api/Invoice68/inHoadon", "sellerFullName": "", "sellerName": "CÔNG TY TNHH MỘT THÀNH VIÊN KẾ SÁCH SÓC TRĂNG", "sellerTaxCode": "**********", "sellerAddress": "Ấp 5<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>"}, "MOBIFONE_TEST": {"tax_code": "**********-998", "username": "55556666TEST", "password": "jdlsOUj98IQ=", "ma_dvcs": "", "login_url": "http://mobiinvoice.vn:9000/api/Account/Login", "get_cctbao_url": "", "link_api": "http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78", "generate_url": "http://mobiinvoice.vn:9000/api/Invoice68/GenerateInvoiceNumber", "cctbao_id": "d9b808ad-f9f1-4487-81a3-************", "template_code": "", "sign_mode": "", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "", "sellerName": "THUANHOA", "sellerTaxCode": "", "sellerAddress": ""}}