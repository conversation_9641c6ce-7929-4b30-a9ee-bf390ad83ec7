import os
import json
import time
import datetime
import threading

#--------- test nssm hoặc winsw ----------------------------------------------------------------------------------
import sys
sys.stdout = open("debug/logs_winsw/auto_backup.out.log", "a", encoding="utf-8")
sys.stderr = open("debug/logs_winsw/auto_backup.err.log", "a", encoding="utf-8")
#-----------------------------------------------------------------------------------------------------------------

from app import app  # 📥 import app Flask

CONFIG_PATH = os.path.join("config", "settings.json")
MYSQLDUMP_PATH = r"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe"

# ✅ Phân tích thông tin từ cấu hình app
uri = app.config["SQLALCHEMY_DATABASE_URI"]
import re
match = re.match(r"mysql\+pymysql://(.*?):(.*?)@.*?/(.*?)(\?.*)?$", uri)
USER, PASSWORD, DB_NAME, _ = match.groups()

def run_backup_once():
    try:
        import pyminizip  # pip install pyminizip
        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            conf = json.load(f)

        folder = conf.get("backupDir", "backup")
        os.makedirs(folder, exist_ok=True)

        sql_file = os.path.join(folder, f"{DB_NAME}_backup.sql")
        zip_file = sql_file + ".zip"
        password = "77997799"

        # Backup SQL
        command = f'"{MYSQLDUMP_PATH}" --no-tablespaces -u {USER} -p{PASSWORD} {DB_NAME} > "{sql_file}"'
        print("⚙️ Lệnh backup:", command)
        result = os.system(f'cmd /c {command}')
        print("✅ Kết quả lệnh:", result)

        if result == 0:
            # Ghi đè ZIP (nếu đã tồn tại)
            if os.path.exists(zip_file):
                os.remove(zip_file)

            pyminizip.compress(sql_file, None, zip_file, password, 5)
            os.remove(sql_file)
            print(f"🔐 Đã tạo và ghi đè: {zip_file}")
            return zip_file
        else:
            return f"❌ Backup lỗi: mã {result}"

    except Exception as e:
        print(f"❌ Backup lỗi: {e}")
        return str(e)


def schedule_auto_backup():
    def loop():
        print("🟢 [AutoBackup] Đã khởi động tiến trình auto backup")
        while True:
            try:
                run_backup_once()
                with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                    delay = int(json.load(f).get("backupInterval", 60))
                print(f"⏳ Đợi {delay} phút trước lần backup tiếp theo...")
                time.sleep(delay * 60)
            except Exception as e:
                print(f"⚠️ Lỗi auto backup: {e}")
                time.sleep(60)
    threading.Thread(target=loop, daemon=True).start()
