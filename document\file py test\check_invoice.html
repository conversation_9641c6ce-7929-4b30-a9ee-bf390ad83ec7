<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON>ản lý hóa đơn</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
	<style>
	  body {
		padding: 20px;
		background-color: #f8f9fa;
		height: 100vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	  }

	  .container-fluid {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	  }

	  .table-wrapper {
		height: 100vh;
		flex: 1;
		overflow-y: auto;
		overflow-x: auto;
		border-radius: 10px;
		border: 2px solid #dee2e6;
		background: white;
	  }

	  .table {
		width: 100%;
		border-collapse: separate;
		border-spacing: 0;
		font-size: 13px;
	  }

	  .table th {
		position: sticky;
		top: 0;
		background-color: #2596be!important;
		color: white !important;
		z-index: 10;
		text-align: center;
		padding: 8px 12px;
		font-size: 13px;
		user-select: none;
		vertical-align: middle;
		white-space: nowrap;
	  }

	  .table td {
		text-align: center;
		vertical-align: middle;
		font-size: 13px;
		padding: 6px 10px;
		border-bottom: 1px solid #dee2e6;
	  }

	  .table tr:hover td {
		background-color: #f1f1f1;
	  }
	  .table tbody tr {
		  border: 1px solid #dee2e6;
		  border-radius: 6px;
		  background: #fff;
		  box-shadow: 0 0 0 1px #f3f3f3 inset;
		  transition: background-color 0.2s ease;
		}

		.table tbody tr:hover {
		  background-color: #f5f9ff;
		}


	  .form-label {
		color: #063970;
		font-weight: 500;
		font-size: 14px;
		user-select: none;
	  }
	  .table tbody tr {
		  border-bottom: 1px solid #dee2e6;
	  }

	  .table tbody tr:last-child {
		  border-bottom: none;
	  }

	  .form-control,
	  .form-select {
		font-size: 13px !important;
		padding: 4px 6px !important;
		min-width: 90px !important;
		height: auto !important;
	  }

	  .stat-number {
		font-weight: bold;
		color: #d32f2f;
		font-size: 13px; //các số tổng
	  }

	  .badge {
		font-size: 13px;
		display: inline-block;
		min-width: 40px;
		text-align: center;
	  }

	  .summary-bar {
		padding: 6px 12px;
		font-size: 13px;
		background: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #dee2e6;
	  }
	</style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });
</script>

</head>
<body>
  <div class="container-fluid">
    <h3 class="text-center mb-2 text-danger fw-bold">QUẢN LÝ GIAO DỊCH ĐÃ XUẤT HÓA ĐƠN</h3>
	<hr class="my-1" style="border-top: 3px solid #0d6efd;" />
    <!-- Bộ lọc -->
    <div class="text-center row g-2 align-items-end mb-3">
      <div class="col-auto">
        <label class="form-label">Từ Ngày</label>
        <input type="date" id="filterStartDate" class="form-control">
      </div>
      <div class="col-auto">
        <label class="form-label">Đến Ngày</label>
        <input type="date" id="filterEndDate" class="form-control">
      </div>
      <div class="col-auto">
        <label class="form-label">Từ Giờ</label>
        <input type="time" id="filterStartTime" class="form-control">
      </div>
      <div class="col-auto">
        <label class="form-label">Đến Giờ</label>
        <input type="time" id="filterEndTime" class="form-control">
      </div>
      <div class="col-auto">
        <label class="form-label">Vòi</label>
        <select id="filterFaucet" class="form-select"></select>
      </div>
      <div class="col-auto">
        <label class="form-label">Nhiên liệu</label>
        <select id="filterFuel" class="form-select"></select>
      </div>
      <div class="col-auto">
        <label class="form-label">Đơn giá</label>
        <select id="filterPrice" class="form-select"></select>
      </div>
      <div class="col-auto">
        <label class="form-label">Mã GD</label>
        <input type="text" id="filterCode" class="form-control">
      </div>
      <div class="col-auto" style="display: none;">
		  <div class="form-check">
			<input class="form-check-input" type="checkbox" id="filterIsInvoiced" checked>
			<label class="form-check-label" for="filterIsInvoiced">
			  GD đã có hóa đơn
			</label>
		  </div>
	  </div>

	  <div class="col-auto">
		<button class="btn btn-success btn-sm" onclick="updateInvoiceNumbers()">Gán số HĐ</button>
	  </div>
      <div class="col-auto">
        <button class="btn btn-danger btn-sm" onclick="resetSelectedInvoices()">Xóa số HĐ</button>
      </div>
    </div>

	<div class="d-flex flex-wrap gap-2 mb-2" style="align-items: start;">
	  <div id="logSummary" class="flex-fill"></div>
	  <div id="invoiceStats" class="flex-fill"></div>
	</div>



    <div class="table-wrapper mt-2">
      <table class="table table-bordered table-hover table-sm mb-0">
		<thead class="table-primary">
		  <tr>
			<th><input type="checkbox" id="select-all" onchange="toggleSelectAll(this)"></th>
			<th class="sortable" onclick="sortTable(1)">NGÀY <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(2)">GIỜ <i class="fa fa-sort"></i></th>
			<th>CÒ</th>
			<th>NHIÊN LIỆU</th>
			<th class="sortable" onclick="sortTable(5)">SỐ TIỀN <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(6)">SỐ LÍT <i class="fa fa-sort"></i></th>
			<th>ĐƠN GIÁ</th>
			<th class="sortable" onclick="sortTable(8)">SỐ HĐ <i class="fa fa-sort"></i></th>
			<th>SERIAL</th>
			<th>BỒN</th>			
			<th>MÃ GD</th>
			<th>TRẠM</th>
		  </tr>
		</thead>
        <tbody id="logResetBody"></tbody>
      </table>
    </div>
  </div>

<script>
let fuelMap = {};
async function loadFuelMap() {
  try {
    const res = await fetch("/fuel_map");
    fuelMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load fuelMap:", e);
    fuelMap = {};
  }
}

function getMappedFuelName(code) {
  return fuelMap?.[code] || code || "";
}



let tankMap = {};
async function loadTankMap() {
  try {
    const res = await fetch("/tank_map");
    tankMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load tankMap:", e);
    tankMap = {};
  }
}

function getMappedTankName(serial) {
  return tankMap?.[serial] || "";
}



let currentLogs = [];
function loadResetLogs() {
fetch('/logs')
  .then(res => res.json())
  .then(data => {
	currentLogs = data.logs;  // ✅ Giữ tất cả log, không lọc trước
	applyResetFilters();
  });
}


function fillSelect(id, values) {
  const select = document.getElementById(id);
  const current = select.value;
  select.innerHTML = '<option value="">Tất cả</option>';

  [...values].sort().forEach(val => {
    const label = (id === "filterFuel") ? (fuelMap[val] || val) : val;
    const text = typeof val === "number" ? val.toLocaleString('vi-VN') : label;
    select.innerHTML += `<option value="${val}">${text}</option>`;
  });

  select.value = current;
}


function updateResetDropdowns(logs) {
  const selectedFaucet = document.getElementById("filterFaucet").value;
  const selectedFuel = document.getElementById("filterFuel").value;
  const selectedPrice = document.getElementById("filterPrice").value;

  const faucets = new Set();
  const fuels = new Set();
  const prices = new Set();

  for (let log of logs) {
    const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
    const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
    const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;

    if (matchFuel && matchPrice) faucets.add(log.faucetNo);
    if (matchFaucet && matchPrice) fuels.add(log.fuelType);
    if (matchFuel && matchFaucet) prices.add(log.transactionPrice);
  }

  fillSelect("filterFaucet", faucets);
  fillSelect("filterFuel", fuels);
  fillSelect("filterPrice", prices);
}





function applyResetFilters() {
	updateResetDropdowns(currentLogs); 
	const onlyInvoiced = document.getElementById("filterIsInvoiced").checked;
	const startDate = document.getElementById("filterStartDate").value;
	const endDate = document.getElementById("filterEndDate").value;
	const startTime = document.getElementById("filterStartTime").value;
	const endTime = document.getElementById("filterEndTime").value;
	const faucet = document.getElementById("filterFaucet").value;
	const fuel = document.getElementById("filterFuel").value;
	const price = document.getElementById("filterPrice").value;
	const code = document.getElementById("filterCode").value.toLowerCase();


	const filtered = currentLogs.filter(log => {
		if (onlyInvoiced && !log.isInvoiced) return false;
		if (startDate && log.transactionDate < startDate) return false;
		if (endDate && log.transactionDate > endDate) return false;
		if (startTime && log.transactionTime < startTime) return false;
		if (endTime && log.transactionTime > endTime) return false;
		if (faucet && String(log.faucetNo) !== faucet) return false;
		if (fuel && log.fuelType !== fuel) return false;
		if (price && String(log.transactionPrice) !== price) return false;
		if (code) {
		  const costStr = String(log.transactionCost).replace(/[.,]/g, '').toLowerCase();
		  const codeStr = log.transactionCode.toLowerCase();
		  if (!codeStr.includes(code) && !costStr.includes(code)) return false;
		}
		return true;

});

renderFilteredResetLogs(filtered);
}

function renderFilteredResetLogs(logs) {
  logs.sort((a, b) => {
    const n1 = parseInt(a.invoiceNumber) || 0;
    const n2 = parseInt(b.invoiceNumber) || 0;
    return n2 - n1;
  });

  const totalInternal = logs.filter(l => l.invoiceNumber === "Nội bộ").length;
  const totalNonInternal = logs.length - totalInternal;

  const tbody = document.getElementById("logResetBody");
  const summary = document.getElementById("logSummary");
  const statsDiv = document.getElementById("invoiceStats");
  tbody.innerHTML = '';

  let sumCost = 0, sumLitre = 0;
  logs.forEach(log => {
    sumCost += log.transactionCost;
    sumLitre += log.transactionAmount / 1000;
  });

  const invoiceObjects = logs
    .map(l => l.invoiceNumber)
    .filter(x => typeof x === "string" && x.trim() !== "" && x !== "CHUA_RO_SOHĐ" && x !== "Nội bộ")
    .map(x => ({
      original: x,
      sortKey: parseInt(x.split('_')[0]) || 0
    }))
    .sort((a, b) => a.sortKey - b.sortKey);

  const invoiceNumbers = invoiceObjects.map(x => x.original);
  const hasUnderscore = invoiceNumbers.some(inv => inv.includes("_"));

  const faucet = document.getElementById("filterFaucet").value;
  const fuel = document.getElementById("filterFuel").value;
  const price = document.getElementById("filterPrice").value;
  const code = document.getElementById("filterCode").value.trim();
  const isStrictFilter = faucet || fuel || price || code;

  let minInvoice = null, maxInvoice = null, missingCount = 0, missingList = [];

  if (isStrictFilter) {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary" style="font-weight: bold;">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label text-danger">Đang lọc, bỏ qua kiểm tra thiếu số HĐ</span>
      </div>
    `;
  } else if (!hasUnderscore && invoiceObjects.length > 0) {
    minInvoice = invoiceObjects[0].sortKey;
    maxInvoice = invoiceObjects[invoiceObjects.length - 1].sortKey;
    const fullSet = new Set(invoiceObjects.map(x => x.sortKey));
    const maxGap = maxInvoice - minInvoice;

    if (maxGap < 5000) {
      for (let i = minInvoice; i <= maxInvoice; i++) {
        if (!fullSet.has(i)) {
          missingCount++;
          missingList.push(i);
        }
      }
    }

    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ nhỏ nhất:</span> <span class="stat-number text-success">${minInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ lớn nhất:</span> <span class="stat-number text-success">${maxInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Thiếu:</span> <span class="stat-number text-danger">${missingCount}</span>
        ${missingList.length > 0 ? `<br/><span class="label">Danh sách thiếu:</span> <span class="stat-number text-danger">${missingList.join(", ")}</span>` : ""}
      </div>
    `;
  } else {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label text-danger">Không kiểm tra thiếu do định dạng số HĐ phức tạp</span>
      </div>
    `;
  }

  summary.innerHTML = `
    <div class="d-flex flex-wrap justify-content-between align-items-center border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
      <div>
        <span class="label">Tổng tiền:</span> <span class="stat-number text-primary">${sumCost.toLocaleString('vi-VN')}</span> đồng
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng lít:</span> <span class="stat-number text-primary">${sumLitre.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</span> lít
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng GD:</span> <span class="stat-number text-primary">${logs.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Nội bộ:</span> <span class="stat-number text-secondary">${totalInternal}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Chưa xuất:</span> <span class="stat-number text-danger">${totalNonInternal - invoiceNumbers.length}</span>
      </div>
    </div>
  `;

  document.getElementById("filterIsInvoicedSummary").addEventListener("change", (e) => {
    document.getElementById("filterIsInvoiced").checked = e.target.checked;
    applyResetFilters();
  });

  // Bảng log
  for (let log of logs) {
    const tr = document.createElement("tr");
    tr.innerHTML = `
      <td><input type="checkbox" class="row-checkbox" value="${log.transactionCode}" /></td>
      <td>${log.transactionDate}</td>
      <td>${log.transactionTime}</td>
      <td>${log.faucetNo}</td>
      <td>${getMappedFuelName(log.fuelType)}</td>		
      <td>${log.transactionCost.toLocaleString('vi-VN')}</td>
      <td>${(log.transactionAmount / 1000).toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</td>
      <td>${log.transactionPrice.toLocaleString('vi-VN')}</td>
		<td>
		  ${
			log.invoiceNumber === "Nội bộ"
			  ? `<span class="badge bg-secondary text-light rounded-pill px-3 py-1">Nội bộ</span>`
				: log.invoiceNumber && log.invoiceNumber !== "CHUA_RO_SOHĐ" && log.invoiceNumber !== "0"
				  ? `<span class="badge bg-success rounded-pill px-3 py-1" style="font-size: 12px;">${log.invoiceNumber}</span>`
				: `<input type="text" class="form-control form-control-sm invoice-input text-center"
						  data-code="${log.transactionCode}" 
						  value="${log.invoiceNumber || ''}" 
						  placeholder="Nhập số HĐ">`
		  }
		</td>

			  <td>${log.pumpSerial}</td>
	  <td>${getMappedTankName(log.pumpSerial)}</td>	  
      <td>${log.transactionCode}</td>
      <td>${log.stationCode || ''}</td>
    `;
    tbody.appendChild(tr);
  }
}




function toggleSelectAll(master) {
	const checkboxes = document.querySelectorAll(".row-checkbox");
	checkboxes.forEach(cb => cb.checked = master.checked);
}

async function resetSelectedInvoices() {
  const selected = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  if (!selected.length) {
    Swal.fire("⚠️", "Vui lòng chọn ít nhất một giao dịch.", "warning");
    return;
  }

  const result = await Swal.fire({
    icon: "question",
    title: "Xác nhận Reset",
    html: `Bạn sắp <b>RESET</b> <span style="color: red;">${selected.length}</span> giao dịch đã xuất hóa đơn.<br>Thao tác này sẽ xóa số HĐ đã gán.`,
    showCancelButton: true,
    confirmButtonText: "Đồng ý",
    cancelButtonText: "Hủy",
    confirmButtonColor: "#d33",
    cancelButtonColor: "#6c757d"
  });

  if (!result.isConfirmed) return;

  const codes = selected.map(cb => cb.value);

  try {
    await Promise.all(
      codes.map(code =>
        fetch('/api/check_invoice', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ transactionCode: code })
        })
      )
    );

    Swal.fire("✅ Đã reset thành công!", "", "success").then(() => {
      const timestamp = Date.now().toString();
      localStorage.setItem("logsResetAt", timestamp);
      window.dispatchEvent(new StorageEvent("storage", {
        key: "logsResetAt",
        newValue: timestamp
      }));
      location.reload();
    });
  } catch (err) {
    console.error(err);
    Swal.fire("❌ Có lỗi xảy ra khi reset.", "", "error");
  }
}




function updateInvoiceNumbers() {
	const inputs = document.querySelectorAll('.invoice-input');
	const updates = [];

	inputs.forEach(input => {
	const code = input.dataset.code;
	const invoice = input.value.trim();
	if (invoice) {
	  updates.push({ code, invoice });
	}
	});

	if (!updates.length) {
	alert("Không có số HĐ nào được nhập.");
	return;
	}

	if (!confirm(`Bạn có chắc muốn gán lại ${updates.length} số HĐ?`)) return;

	Promise.all(
	updates.map(item => 
	  fetch('/api/assign_invoice_number', {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({
		  transactionCode: item.code,
		  invoiceNumber: item.invoice
		})
	  })
	)
	).then(() => {
	alert("✅ Đã gán lại số hóa đơn.");
	loadResetLogs();
	});
}

function sortTable(colIndex) {
	const table = document.querySelector("table");
	const tbody = table.querySelector("tbody");
	const rows = Array.from(tbody.querySelectorAll("tr"));

	const asc = table.dataset.sortCol == colIndex && table.dataset.sortDir !== "asc";
	table.dataset.sortCol = colIndex;
	table.dataset.sortDir = asc ? "asc" : "desc";

	rows.sort((a, b) => {
	let v1 = a.cells[colIndex].textContent.trim();
	let v2 = b.cells[colIndex].textContent.trim();

	const isNumber = !isNaN(v1.replace(/,/g, "")) && !isNaN(v2.replace(/,/g, ""));
	if (isNumber) {
	  v1 = parseFloat(v1.replace(/,/g, ""));
	  v2 = parseFloat(v2.replace(/,/g, ""));
	}

	return asc ? (v1 > v2 ? 1 : -1) : (v1 < v2 ? 1 : -1);
	});

	tbody.innerHTML = "";
	rows.forEach(row => tbody.appendChild(row));
}

function getTodayVN() {
	const now = new Date();
	now.setHours(now.getHours() + 7);
	return now.toISOString().slice(0, 10);
}

function setDefaultDates() {
  const todayStr = getTodayVN();
  document.getElementById("filterStartDate").value = todayStr;
  document.getElementById("filterEndDate").value = todayStr;

  // Bỏ tích mặc định ô "Hiển thị GD đã có hóa đơn"
  document.getElementById("filterIsInvoiced").checked = false;
}

async function validateDateRange_Check(showPopup = true) {
  const startInput = document.getElementById('filterStartDate');
  const endInput = document.getElementById('filterEndDate');

  const start = new Date(startInput.value);
  const end = new Date(endInput.value);

  if (!start || !end || isNaN(start) || isNaN(end)) return false;

  const diffMs = end.getTime() - start.getTime();
  const diffDays = diffMs / (1000 * 60 * 60 * 24);

  if (diffDays > 92) {
    await Swal.fire({
      icon: 'warning',
      title: 'Khoảng thời gian quá dài!',
      html: `Bạn chỉ được chọn tối đa <b>90 ngày</b>.<br>Hiện tại đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
    return false;
  }

  if (diffDays > 0 && showPopup) {
    await Swal.fire({
      icon: 'info',
      html: `Bạn đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
  }

  return true;
}

window.onload = async function () {
    setDefaultDates();
    await loadFuelMap();	
    await loadTankMap();
    loadResetLogs();

    // Gắn onchange/input để lọc tự động
    document.getElementById("filterStartDate").onchange = applyResetFilters;
    document.getElementById("filterEndDate").onchange = applyResetFilters;
	
	document.getElementById("filterStartDate").onchange = async () => {
	  if (await validateDateRange_Check()) applyResetFilters();
	};
	document.getElementById("filterEndDate").onchange = async () => {
	  if (await validateDateRange_Check()) applyResetFilters();
	};


    document.getElementById("filterStartTime").onchange = applyResetFilters;
    document.getElementById("filterEndTime").onchange = applyResetFilters;
    document.getElementById("filterFaucet").onchange = applyResetFilters;
    document.getElementById("filterFuel").onchange = applyResetFilters;
    document.getElementById("filterPrice").onchange = applyResetFilters;
    document.getElementById("filterCode").oninput = applyResetFilters;
	
	document.getElementById("filterFaucet").onchange = applyResetFilters;
	document.getElementById("filterFuel").onchange = applyResetFilters;
	document.getElementById("filterPrice").onchange = applyResetFilters;

};


</script>
</body>
</html>
