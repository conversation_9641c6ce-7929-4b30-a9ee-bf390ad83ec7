import json
import requests
from num2words import num2words
from datetime import datetime
from html import escape

import logging
from logging.handlers import RotatingFileHandler
import os

os.makedirs("debug/logs_send_invoice", exist_ok=True)
logger = logging.getLogger("send_invoice")
logger.setLevel(logging.INFO)

if not logger.handlers:
    handler = RotatingFileHandler("debug/logs_send_invoice/send_invoice.log", maxBytes=1_000_000, backupCount=10, encoding="utf-8")
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)          
#-----------------------------------------------------------------------------------------------------------------
# === Đọc cấu hình provider và thông tin API động mỗi lần gọi ===
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), "config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)
#-----------------------------------------------------------------------------------------------------------------
# === Đọc cấu hình ánh xạ tên nhiên liệu từ fuel.json và setting ===
def map_fuel_name(code):
    try:
        fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
        with open(fuel_path, encoding="utf-8") as f:
            mapping = json.load(f)
        return mapping.get(code, code)
    except Exception as e:
        logger.info(f"⚠️ Không thể load fuel.json: {e}")
        return code

#-----------------------------------------------------------------------------------------------------------------
def send_invoice_from_log(log, preview=False, custom_data=None, from_auto=False):
    config = load_config()
    
    provider = config.get("provider", "VNPT").upper()
    conf = config.get(provider, {})  # chữ HOA đúng với key trong file


    # ✅ Chỉ gán provider nếu là xuất thật
    if not preview:
        log.provider = provider.lower()

    if provider == "VNPT":
        return handle_vnpt(log, preview, custom_data, conf, from_auto=from_auto)
    elif provider == "MOBIFONE":
        return handle_mobifone(log, preview, custom_data, conf)
    elif provider == "VIETTEL":
        return handle_viettel(log, preview, custom_data, conf)
    elif provider == "MISA":
        return handle_misa(log, preview, custom_data, conf)
    elif provider == "EASYINVOICE":
        return handle_easyinvoice(log, preview, custom_data, conf) 
    elif provider == "BKAV":
        return handle_bkav(log, preview, custom_data, conf)
    elif provider == "FAST":
        return handle_fast(log, preview, custom_data, conf) 
    elif provider == "HILO":
        return handle_hilo(log, preview, custom_data, conf) 
    elif provider == "MOBIFONE_TEST":
        return handle_mobifone_test(log, preview, custom_data, conf)        
    else:
        return {"success": False, "message": f"Chưa hỗ trợ hóa đơn: {provider}"}

#========== VNPT ===============================================================================
def handle_vnpt(log, preview, custom_data, conf, from_auto=False):
    from datetime import datetime
    import os, requests, html, re
    from num2words import num2words
    from app import db  # thêm nếu chưa có

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')
    date_folder = today.strftime('%Y%m%d')

    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()
    
    raw_thue = str(custom_data.get("thueStr", custom_data.get("thue", "10"))).strip()

    if raw_thue == "KKKNT":
        thue = 0
        thue_suat_gui = -2
    elif raw_thue == "KCT":
        thue = 0
        thue_suat_gui = -1
    else:
        try:
            thue = float(raw_thue)
            thue_suat_gui = thue
        except:
            thue = 10
            thue_suat_gui = 10

    mkhang = custom_data.get("mkhang", "").strip()
    thhdv = map_fuel_name(log.fuelType)
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()
    phone = custom_data.get("phone", "").strip()
    cccd = custom_data.get("cccd", "").strip()


     # === Tính toán lại giống frontend ===
    sl = log.transactionAmount / 1000
    price_vat = log.transactionPrice or 0
    cost_vat = log.transactionCost or 0

    discount_per_lit_vat = float(custom_data.get("discountPerLitVat", 0) or 0)

    if discount_per_lit_vat > 0:
        discount_per_lit = round(discount_per_lit_vat / (1 + thue / 100), 4)
        discount_total = round(sl * discount_per_lit)
    else:
        discount_per_lit = 0
        discount_total = 0

    # === Khởi tạo biến ===
    dg_chua_vat = 0
    tt_chua_vat = 0
    tien_thue = 0
    tong_cong = 0
    discount_per_lit = 0
    discount_total = 0
    congtienhang = 0

    if discount_per_lit_vat > 0:
        discount_per_lit = round(discount_per_lit_vat / (1 + thue / 100), 4)
        discount_total = round(sl * discount_per_lit)

        dg_chua_vat = round(price_vat / (1 + thue / 100), 4)
        tt_chua_vat = round(cost_vat / (1 + thue / 100))

        congtienhang = tt_chua_vat - discount_total
        tien_thue = round(congtienhang * thue / 100)
        tong_cong = congtienhang + tien_thue
    else:
        dg_chua_vat = round(price_vat / (1 + thue / 100), 4)
        tt_chua_vat = round(cost_vat / (1 + thue / 100))
        congtienhang = tt_chua_vat
        tong_cong = cost_vat
        tien_thue = round(cost_vat - tt_chua_vat)

    cost_text = num2words(round(tong_cong), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    # === XML hàng hóa ===
    hhdvu_xml = f'''
    <DSHHDVu>
      <HHDVu>
        <TChat>1</TChat>
        <STT>1</STT>
        <MHHDVu>{escape(log.fuelType)}</MHHDVu>
        <THHDVu>{thhdv}</THHDVu>
        <DVTinh>Lít</DVTinh>
        <SLuong>{sl:.3f}</SLuong>
        <DGia>{dg_chua_vat:.4f}</DGia>
        <ThTien>{round(tt_chua_vat)}</ThTien>
        <TSuat>{thue_suat_gui}</TSuat>
        <TThue>0</TThue>
        <TSThue>{round(tt_chua_vat)}</TSThue>
      </HHDVu>'''

    if discount_total > 0:
        hhdvu_xml += f'''
      <HHDVu>
        <TChat>3</TChat>
        <STT>2</STT>
        <MHHDVu>CKTM</MHHDVu>
        <THHDVu>Chiết khấu thương mại</THHDVu>
        <DVTinh>Lít</DVTinh>
        <SLuong>{sl:.3f}</SLuong>
        <DGia>{discount_per_lit:.4f}</DGia>
        <ThTien>{round(discount_total)}</ThTien>
        <TSuat>{thue_suat_gui}</TSuat>
        <TThue>0</TThue>
        <TSThue>{round(discount_total)}</TSThue>
      </HHDVu>'''

    hhdvu_xml += '</DSHHDVu>'

    # === XML đầy đủ ===
    xml_content = f'''<DSHDon>
      <HDon>
        <key>{log.transactionCode}</key>
        <DLHDon>
          <TTChung>
            <NLap>{today_str}</NLap>
            <DVTTe>VND</DVTTe>
            <TGia>1</TGia>
            {"<HTTToan>" + escape(httt) + "</HTTToan>" if httt else ""}
            <TTKhac>
                <TTin>
                  <TTruong>Extra</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra test</DLieu>
                </TTin>
                <TTin>
                  <TTruong>Extra1</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra1 test</DLieu>
                </TTin>
                <TTin>
                  <TTruong>Extra2</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra2 test</DLieu>
                </TTin>
            </TTKhac>
          </TTChung>
          <NDHDon>
            <NBan>
              <TTKhac>
                <TTin>
                  <TTruong>Extra3</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>{escape(plate)}</DLieu>
                </TTin>
              </TTKhac>
            </NBan>
            <NMua>
              {"<HVTNMHang>" + escape(ho_ten) + "</HVTNMHang>" if ho_ten else ""}
              {"<Ten>" + escape(ten) + "</Ten>" if ten else ""}
              {"<MST>" + escape(mst) + "</MST>" if mst else ""}
              {"<DChi>" + escape(dchi) + "</DChi>" if dchi else ""}
              {"<MKHang>" + escape(mkhang) + "</MKHang>" if mkhang else ""}
              <DCTDTu>{escape(email)}</DCTDTu>
              <SDThoai>{escape(phone)}</SDThoai>
              <CCCDan>{escape(cccd)}</CCCDan>
              <TTKhac>
                <TTin>
                  <TTruong>Extra6</TTruong>
                  <KDLieu>string</KDLieu>
                  {"<DLieu>" + escape(mkhang) + "</DLieu>" if mkhang else ""}
                </TTin>
              </TTKhac>
            </NMua>
            {hhdvu_xml}
            <TToan>
              <THTTLTSuat>
                <LTSuat>
                  <TSuat>{thue_suat_gui}</TSuat>
                  <TThue>{tien_thue}</TThue>
                  <ThTien>{round(congtienhang)}</ThTien>
                </LTSuat>
              </THTTLTSuat>
              <TgTCThue>{round(congtienhang)}</TgTCThue>
              <TgTThue>{tien_thue}</TgTThue>
              <TTCKTMai>{round(discount_total)}</TTCKTMai>
              <TgTTTBSo>{tong_cong}</TgTTTBSo>
              <TgTTTBChu>{cost_text}</TgTTTBChu>
            </TToan>
          </NDHDon>
        </DLHDon>
      </HDon>
    </DSHDon>'''

    # === Trả kết quả nếu preview ===
    if preview:
        return {
            "success": True,
            "xml": xml_content.strip(),
            "sl": round(sl, 3),
            "dgChuaVat": round(dg_chua_vat, 4),
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": tien_thue,
            "ttVat": tong_cong,
            "chu": cost_text,
            "ho_ten": ho_ten,
            "ten": ten,
            "mst": mst,
            "dchi": dchi,
            "httt": httt,
            "thue": thue_suat_gui,
            "mkhang": mkhang,
            "thhdv": thhdv,
            "fuelType": log.fuelType,
            "discountPerLit": round(discount_per_lit, 4),
            "discountTotal": round(discount_total),
            "congtienhang": round(congtienhang)
        }



    # Gửi hóa đơn qua SOAP
    soap_xml = f'''<?xml version="1.0" encoding="utf-8"?>
    <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                     xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
      <soap12:Body>
        <ImportAndPublishInvMTT xmlns="http://tempuri.org/">
          <Account>{conf["Account"]}</Account>
          <ACpass>{conf["ACpass"]}</ACpass>
          <xmlInvData><![CDATA[{xml_content}]]></xmlInvData>
          <username>{conf["username"]}</username>
          <password>{conf["password"]}</password>
          <pattern>{conf["pattern"]}</pattern>
          <serial>{conf["serial"]}</serial>
          <convert>{conf["convert"]}</convert>
        </ImportAndPublishInvMTT>
      </soap12:Body>
    </soap12:Envelope>'''

    url = conf["link_api"]
    headers = { "Content-Type": "application/soap+xml; charset=utf-8" }
    response = requests.post(url, data=soap_xml.encode("utf-8"), headers=headers)
    vnpt_response = response.text.strip()

    # ✅ Ghi file debug
    debug_dir = os.path.join("debug", date_folder)
    os.makedirs(debug_dir, exist_ok=True)

    # ✅ Trích số hóa đơn từ chuỗi phản hồi VNPT
    match = re.search(r"OK:.*?-(\d{10,20})</ImportAndPublishInvMTTResult>", vnpt_response)
    shdon = match.group(1) if match else None
    is_success = shdon is not None
    logger.info(f"🧠 Số hóa đơn trích được: {shdon} | is_success: {is_success}")

    filename = f"{log.transactionCode}_vnpt.txt" if is_success else f"{log.transactionCode}_vnpt_ERR.txt"
    full_file_path = os.path.join(debug_dir, filename)

    # ✅ Ghi SOAP + XML + Response vào file
    pretty_soap = re.sub(
        r"<xmlInvData>.*?</xmlInvData>",
        "<xmlInvData><></xmlInvData>",
        soap_xml,
        flags=re.DOTALL
    )

    with open(full_file_path, "w", encoding="utf-8") as f:
        f.write("SOAP:\n")
        f.write(pretty_soap.strip() + "\n\n")
        f.write("XML:\n")
        f.write(html.unescape(xml_content.strip()) + "\n\n")
        f.write("VNPT Response:\n")
        f.write(html.unescape(vnpt_response))

        # ✅ Nếu thành công → lưu vào DB và trả kết quả phù hợp
        if is_success:
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.isInvoiced = True
            db.session.commit()
            logger.info(f"✅ Số hóa đơn là: {shdon}")
            if from_auto:
                logger.info("✅ TRẢ DICT CHO BACKEND (from_auto=True)")
                return {
                    "success": True,
                    "invoiceNumber": shdon,
                    "message": "Đã gửi hóa đơn thành công",
                    "response": vnpt_response
                }
            else:
                logger.info("✅ TRẢ STRING CHO FRONTEND (from_auto=False)")
                return vnpt_response

        # ❌ Nếu không thành công
        if from_auto:
            logger.info("❌ TRẢ LỖI CHO BACKEND (from_auto=True)")
            return {
                "success": False,
                "invoiceNumber": None,
                "message": "VNPT trả về lỗi hoặc không rõ kết quả",
                "response": vnpt_response or "Không có phản hồi từ VNPT"
            }
        else:
            logger.info("❌ TRẢ STRING LỖI CHO FRONTEND (from_auto=False)")
            return vnpt_response or "Không có phản hồi từ VNPT"

#========== MOBIFONE =======================================================================================
# Hóa đơn GTGT có mã tạo từ máy tính tiền
# Có số hóa đơn , nếu có HSM thì ký, không có thì bỏ qua
def handle_mobifone(log, preview, custom_data, conf):
    from datetime import datetime
    import os
    import json
    import requests
    from num2words import num2words
    from app import db

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')

    # 🛑 CHẶN GỬI LẠI nếu log đã có hóa đơn
    if not preview:
        if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() not in ["", "CHUA_RO_SOHĐ"]):
            return {
                "success": False,
                "message": f"Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}"
            }

    cost_text = num2words(round(log.transactionCost), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    # 🔢 Dữ liệu người mua
    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()
    phone = custom_data.get("phone", "").strip()
    cccd = custom_data.get("cccd", "").strip()

    raw_thue = custom_data.get("thue", 10)
    try:
        thue = int(float(raw_thue))
    except (TypeError, ValueError):
        thue = 0

    # 🔍 Tính toán dữ liệu hóa đơn
    thhdv = map_fuel_name(log.fuelType)
    sl = round(log.transactionAmount / 1000, 4)
    dg_chua_vat = log.transactionPrice / (1 + thue / 100)
    tt_chua_vat = log.transactionCost / (1 + thue / 100)
    tong_cong = log.transactionCost
    tien_thue = tong_cong - tt_chua_vat

    try:
        # 🔐 Gọi API lấy token
        token_res = requests.post(conf["login_url"], json={
            "username": conf["username"],
            "password": conf["password"]
        }, timeout=10)

        logger.info("📥 Token response: " + token_res.text)
        token_json = token_res.json()

        # ✅ Lấy token và ma_dvcs rõ ràng
        token = token_json.get("token") or token_json.get("access_token")
        ma_dvcs = token_json.get("ma_dvcs") or conf.get("ma_dvcs")

        if not token or not ma_dvcs:
            return {"success": False, "message": f"Không lấy được token Mobifone.\nChi tiết: {token_res.text}"}


        # ✅ Gọi API lấy mẫu số
        cctbao_id = None
        try:
            get_url = conf.get("get_cctbao_url", "").strip()
            if get_url:
                full_url = f"{get_url}?refId=RF00059"
                headers = {
                    "Authorization": f"Bear {token};{ma_dvcs}",
                }
                logger.info("📤 Gọi API URL: " + full_url)
                res = requests.get(full_url, headers=headers, timeout=10)
                if res.ok:
                    data = res.json()
                    logger.info(f"📥 Mobifone trả {len(data)} mẫu số")
                    preferred_template = conf.get("template_code", "").strip()
                    found_template = None

                    for item in data:
                        if item.get("value") == preferred_template:
                            cctbao_id = item.get("id") or item.get("qlkhsdung_id")
                            found_template = f"{item.get('value')} - ID: {cctbao_id}"
                            break

                    if found_template:
                        logger.info(f"✅ Mẫu được chọn: {found_template}")
                    else:
                        logger.info(f"⚠️ Không tìm thấy mẫu phù hợp với template_code: {preferred_template}")
                    
                    preferred_template = conf.get("template_code", "").strip()

                    if data:
                        for item in data:
                            if item.get("value") == preferred_template:
                                cctbao_id = item.get("id") or item.get("qlkhsdung_id")
                                logger.info(f"✅ Đã chọn mẫu số: {item.get('value')} - ID: {cctbao_id} - KH: {item.get('khhdon')}")
                                break

        except Exception as ex:
            logger.info(f"❌ Lỗi khi gọi API mẫu số Mobifone: {ex}")


        # 🛑 Fallback nếu không có mẫu số từ API
        if not all([cctbao_id]):
            logger.info("⚠️ API không trả mẫu số – fallback config.json")
            cctbao_id = conf.get("cctbao_id")

        # ⛔ Không có gì cả → lỗi
        if not all([cctbao_id]):
            return {"success": False, "message": "Không tìm được mẫu số hóa đơn để tạo"}

        # 🧾 Tạo payload
        payload = {
            "editmode": 1,
            "tax_code": conf["tax_code"],
            "data": [{
                "cctbao_id": cctbao_id,
                "nlap": today_str,
                "dvtte": "VND",
                "tgia": 1,
                "htttoan": httt,
                "mnmua": "",
                "mst": mst,
                "tnmua": ten,
                "email": email,
                "ten": ho_ten,
                "dchi": dchi,
                "sdtnmua": phone,
                "cmndmua": cccd,
                "tgtcthue": round(tt_chua_vat),
                "tgtthue": round(tien_thue),
                "tgtttbso": round(tong_cong),
                "tgtttbso_last": round(tong_cong),
                "tkcktmn": 0.0,
                "ttcktmai": 0.0,
                "tgtphi": 0.0,
                "mdvi": ma_dvcs,
                "tthdon": 0,
                "is_hdcma": 1,
                "details": [{
                    "data": [{
                        "id": 0,
                        "stt": 1,
                        "ma": log.fuelType,
                        "ten": thhdv,
                        "mdvtinh": "Lít",
                        "dgia": f"{dg_chua_vat:.4f}",
                        "kmai": "1",
                        "tsuat": thue,
                        "thtien": str(round(tt_chua_vat)),
                        "sluong": f"{sl:.3f}",
                        "tlckhau": 0,
                        "stckhau": 0,
                        "tthue": str(round(tien_thue)),
                        "tgtien": str(round(tong_cong))
                    }]
                }],
                "hoadon68_phi": [{
                    "data": [{
                        "tienphi": 0,
                        "tnphi": "Phí môi trường"
                    }]
                }],
                "hoadon68_khac": [{
                    "data": [{
                        "dlieu": "Địa chỉ",
                        "kdlieu": "decimal",
                        "ttruong": "dclhe"
                    }]
                }]
            }]
        }

        if preview:
            return {
                "success": True,
                "json": payload,
                "nlap": today_str,
                "sl": f"{sl:.3f}",
                "dgChuaVat": f"{dg_chua_vat:.4f}",
                "ttChuaVat": round(tt_chua_vat),
                "tienThue": round(tien_thue),
                "ttVat": round(tong_cong),
                "chu": cost_text,
                "thhdv": thhdv,
                "fuelType": log.fuelType
            }

        # Gửi tạo hóa đơn và sinh số
        result = handle_mobifone_submit(log, payload, conf, token, ma_dvcs)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType
        })
        return result

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn MOBIFONE: {str(e)}"}
        
# Nếu chờ ký, ký sau, chưa có số hóa đơn thì tạm gán mã GD lưu vào db, trên index là 0
def handle_mobifone_submit(log, payload, conf, token, ma_dvcs):
    import requests
    from app import db
    import json
    import traceback

    try:
        headers = {
            "Authorization": f"Bear {token};{ma_dvcs}",
            "Content-Type": "application/json"
        }

        # Gửi API SaveAndSignHoadon78
        #logger.info(f"📤 Gửi API SaveAndSignHoadon78 với payload: {json.dumps(payload, ensure_ascii=False)}")
        res = requests.post(conf["link_api"], json=payload, headers=headers)
        #logger.info(f"📥 Raw response text: {res.text}")

        if not res.ok:
            logger.info(f"❌ Status code: {res.status_code}")
            return {"success": False, "message": f"Lỗi gọi SaveAndSignHoadon78: {res.status_code} - {res.text}"}

        res_json = res.json()
        #logger.info(f"📥 Parsed JSON: {json.dumps(res_json, ensure_ascii=False)}")
        # ---------------------------------------------------------------------------------
        
        res0 = res_json[0] if isinstance(res_json, list) and len(res_json) > 0 else {}
        data_raw = res0.get("data", {})

        # ✅ Chuẩn hóa data nếu là list
        if isinstance(data_raw, list) and len(data_raw) > 0:
            data = data_raw[0]
        elif isinstance(data_raw, dict):
            data = data_raw
        else:
            logger.warning("❌ Mobifone trả về data không hợp lệ")
            return {"success": False, "message": "Mobifone trả về dữ liệu không hợp lệ"}

        # 🧠 Trích đúng hdon_id – luôn là UUID trả về từ Mobifone
        hdon_id = data.get("hdon_id")
        if not hdon_id:
            logger.warning("❌ Mobifone không trả về hdon_id hợp lệ! KHÔNG được fallback ID_...")
            return {"success": False, "message": "Mobifone không trả về mã hdon_id hợp lệ."}

        shdon = data.get("shdon") or log.transactionCode
        tthai = data.get("tthai") or res0.get("tthai", "")

        # ---------------------------------------------------------------------------------
        logger.info(f"🧠 Đã trích: hdon_id = {hdon_id}, shdon = {shdon}, tthai = {tthai}")
        
        # Ghi lại log file
        date_folder = datetime.now().strftime('%Y%m%d')
        debug_dir = os.path.join("debug", date_folder)
        os.makedirs(debug_dir, exist_ok=True)

        # Tùy theo có shdon hay không mà đổi tên file
        if shdon:
            log_filename = f"{log.transactionCode}_mobifone.txt"
        else:
            log_filename = f"{log.transactionCode}_mobifone_err.txt"

        with open(os.path.join(debug_dir, log_filename), "w", encoding="utf-8") as f:
            json.dump({
                "username": conf.get("username", ""),
                "password": conf.get("password", ""),
                "token": token,
                "ma_dvcs": ma_dvcs,
                "hdon_id": hdon_id,    
                "fkey": log.transactionCode,                
                "payload": payload,
                "response_create": res_json
            }, f, ensure_ascii=False, indent=2)

        # ✅ Nếu thành công
        if tthai in ["Đã ký", "Đã gửi"]:
            log.invoiceId = hdon_id
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.status = "OK"
            log.provider = "mobifone"
            log.isInvoiced = True
            db.session.commit()
            logger.info(f"✅ Lưu vào DB: Số HĐ = {shdon}, Trạng thái = {tthai}")
            return {
                "success": True,
                "invoiceNumber": shdon,
                "message": f"Hóa đơn đã {tthai.lower()}"
            }

        # ❗ Trường hợp chưa ký
        logger.info(f"⚠️ Hóa đơn chưa ký: trạng thái = {tthai}")
        # ✅ Nếu đã có số hóa đơn thì vẫn lưu vào DB
        if shdon:
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.invoiceId = hdon_id
            log.isInvoiced = True
            log.provider = "mobifone"
            db.session.commit()
            logger.info(f"✅ Đã lưu HĐ (dù chưa ký): {shdon}")
        return {
            "success": True,
            "invoiceNumber": shdon,
            "message": f"Hóa đơn đã tạo nhưng chưa ký – Trạng thái: {tthai or 'N/A'}"
        }

    except Exception as e:
        logger.info(f"❌ Exception: {str(e)}")
        logger.info(traceback.format_exc())
        return {"success": False, "message": f"Lỗi khi gửi SaveAndSignHoadon78: {str(e)}"}

#========== MOBIFONE_TEST ===============================================================================
# Hóa đơn GTGT không mã
# Có số hóa đơn test, chưa ký
def handle_mobifone_test(log, preview, custom_data, conf):
    from datetime import datetime
    import os
    import json
    import requests
    from num2words import num2words
    from app import db

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')

    # 🛑 CHẶN GỬI LẠI nếu log đã có hóa đơn
    if not preview:
        if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() not in ["", "CHUA_RO_SOHĐ"]):
            return {
                "success": False,
                "message": f"Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}"
            }

    cost_text = num2words(round(log.transactionCost), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    # 🔢 Dữ liệu người mua
    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()

    raw_thue = custom_data.get("thue", 10)
    try:
        thue = int(float(raw_thue))
    except (TypeError, ValueError):
        thue = 0

    # 🔍 Tính toán dữ liệu hóa đơn
    thhdv = map_fuel_name(log.fuelType)
    sl = round(log.transactionAmount / 1000, 4)
    dg_chua_vat = log.transactionPrice / (1 + thue / 100)
    tt_chua_vat = log.transactionCost / (1 + thue / 100)
    tong_cong = log.transactionCost
    tien_thue = tong_cong - tt_chua_vat

    try:
        token_res = requests.post(conf["login_url"], json={
            "username": conf["username"],
            "password": conf["password"],
            "tax_code": conf["tax_code"]
        })
        token_json = token_res.json()
        token = token_json.get("token")
        ma_dvcs = token_json.get("ma_dvcs")

        if not token or not ma_dvcs:
            return {"success": False, "message": "Không lấy được token Mobifone."}
            
        # 🧾 Tạo payload
        payload = {
            "editmode": 1,
            "tax_code": conf["tax_code"],
            "data": [{
                "cctbao_id": conf["cctbao_id"],
                "nlap": today_str,
                "dvtte": "VND",
                "tgia": 1,
                "htttoan": httt,
                "mnmua": "",
                "mst": mst,
                "tnmua": ho_ten,
                "email": email,
                "ten": ten,
                "dchi": dchi,
                "sdtnmua": "",
                "cmndmua": "",
                "tgtcthue": round(tt_chua_vat),
                "tgtthue": round(tien_thue),
                "tgtttbso": round(tong_cong),
                "tgtttbso_last": round(tong_cong),
                "tkcktmn": 0.0,
                "ttcktmai": 0.0,
                "tgtphi": 0.0,
                "mdvi": ma_dvcs,
                "tthdon": 0,
                "is_hdcma": 1,
                "details": [{
                    "data": [{
                        "id": 0,
                        "stt": 1,
                        "ma": log.fuelType,
                        "ten": thhdv,
                        "mdvtinh": "Lít",
                        "dgia": f"{dg_chua_vat:.4f}",
                        "kmai": "1",
                        "tsuat": thue,
                        "thtien": str(round(tt_chua_vat)),
                        "sluong": str(sl),
                        "tlckhau": 0,
                        "stckhau": 0,
                        "tthue": str(round(tien_thue)),
                        "tgtien": str(round(tong_cong))
                    }]
                }],
                "hoadon68_phi": [{
                    "data": [{
                        "tienphi": 0,
                        "tnphi": "Phí môi trường"
                    }]
                }],
                "hoadon68_khac": [{
                    "data": [{
                        "dlieu": "Địa chỉ",
                        "kdlieu": "decimal",
                        "ttruong": "dclhe"
                    }]
                }]
            }]
        }

        if preview:
            return {
                "success": True,
                "json": payload,
                "nlap": today_str,
                "sl": sl,
                "dgChuaVat": f"{dg_chua_vat:.4f}",
                "ttChuaVat": round(tt_chua_vat),
                "tienThue": round(tien_thue),
                "ttVat": round(tong_cong),
                "chu": cost_text,
                "thhdv": thhdv,
                "fuelType": log.fuelType
            }

        # 📨 Gửi tạo hóa đơn và sinh số
        result = handle_mobifone_submit_test(log, payload, conf, token, ma_dvcs)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType
        })
        return result

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn MOBIFONE: {str(e)}"}

# Nếu chờ ký, có số hóa đơn thì lấy đúng số hóa đơn hiển thị trên index 
def handle_mobifone_submit_test(log, payload, conf, token, ma_dvcs):
    import os
    import json
    from datetime import datetime
    import requests
    from app import db  # đảm bảo có import db nếu chưa có

    try:
        headers = {
            "Authorization": f"Bearer {token};{ma_dvcs}",
            "Content-Type": "application/json"
        }

        # 🟠 Gửi tạo hóa đơn
        res = requests.post(conf["link_api"], json=payload, headers=headers)
        res_json = res.json()

        hdon_id = res_json[0]['data']['hdon_id'] if res_json and 'data' in res_json[0] else None

        # 🟡 Duyệt hóa đơn
        approve_payload = {"data": [{"lsthdon_id": [hdon_id]}], "tax_code": None}

        # 🔵 Gửi yêu cầu sinh số hóa đơn
        gen_res = requests.post(conf.get("generate_url"), json=approve_payload, headers=headers)
        gen_json = gen_res.json() if gen_res.ok else {}

        # 🧠 ✅ Trích số hóa đơn trực tiếp từ res_json
        shdon = None
        try:
            if isinstance(res_json, list) and len(res_json) > 0:
                shdon = res_json[0].get("data", {}).get("shdon")
        except Exception as e:
            logger.info(f"❌ Không thể lấy shdon: {e}")

        # ✅ Ghi lại log file
        date_folder = datetime.now().strftime('%Y%m%d')
        debug_dir = os.path.join("debug", date_folder)
        os.makedirs(debug_dir, exist_ok=True)

        # 🔥 Tùy theo có shdon hay không mà đổi tên file
        if shdon:
            log_filename = f"{log.transactionCode}_mobifone_test.txt"
        else:
            log_filename = f"{log.transactionCode}_mobifone_test_err.txt"

        with open(os.path.join(debug_dir, log_filename), "w", encoding="utf-8") as f:
            json.dump({
                "username": conf.get("username", ""),
                "password": conf.get("password", ""),
                "token": token,
                "ma_dvcs": ma_dvcs,
                "hdon_id": hdon_id,    
                "fkey": log.transactionCode,                
                "payload": payload,
                "response_create": res_json
            }, f, ensure_ascii=False, indent=2)

        # ✅ Nếu có shdon thì cập nhật log vào DB
        if shdon:
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.isInvoiced = True
            log.provider = "mobifone"
            log.invoiceId = hdon_id
            db.session.commit()
            logger.info(f"🧾 Số hóa đơn đã lưu vào DB: {shdon}")
        else:
            logger.info("⚠️ Không tìm thấy số hóa đơn trong response.")

        return {
            "success": True,
            "hdon_id": hdon_id,
            "invoiceId": hdon_id,
            "shdon": shdon,
            "response": res_json
        }

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn MOBIFONE_TEST: {str(e)}"}
        
        