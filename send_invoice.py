import json
import requests
from num2words import num2words
from datetime import datetime
from html import escape
import logging
from logging.handlers import RotatingFileHandler
import os, html, traceback
import re
import xml.etree.ElementTree as ET

# Thiết lập logging
os.makedirs("debug/logs_send_invoice", exist_ok=True)
logger = logging.getLogger("send_invoice")
logger.setLevel(logging.INFO)

if not logger.handlers:
    handler = RotatingFileHandler("debug/logs_send_invoice/send_invoice.log", maxBytes=1_000_000, backupCount=10, encoding="utf-8")
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

#-----------------------------------------------------------------------------------------------------------------
# === Đọc cấu hình provider và thông tin API động mỗi lần gọi ===
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), "config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

#-----------------------------------------------------------------------------------------------------------------
# === Đọc cấu hình ánh xạ tên nhiên liệu từ fuel.json và setting ===
def map_fuel_name(code):
    try:
        fuel_path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
        with open(fuel_path, "r", encoding="utf-8") as f:
            mapping = json.load(f)
        return mapping.get(code, code)
    except Exception as e:
        logger.info(f"⚠️ Không thể load fuel.json: {e}")
        return code

#-----------------------------------------------------------------------------------------------------------------
def send_invoice_from_log(log, preview=False, custom_data=None, api_account=None, api_pass=None, from_auto=False):
    config = load_config()
    
    provider = config.get("provider", "VNPT").upper()
    conf = config.get(provider, {})  # chữ HOA đúng với key trong file
    
    # Nếu có api_account và api_pass từ user, ưu tiên sử dụng chúng
    logger.info(f"🔍 DEBUG: api_account={api_account}, api_pass={api_pass}")
    logger.info(f"🔍 DEBUG: conf trước khi thay đổi - Account={conf.get('Account')}, ACpass={conf.get('ACpass')}")

    if api_account and api_pass:
        conf["Account"] = api_account
        conf["ACpass"] = api_pass
        logger.info(f"✅ ĐÃ THAY ĐỔI: Sử dụng API credentials của user - Account={api_account}")
    else:
        logger.info(f"⚠️ KHÔNG THAY ĐỔI: Sử dụng config mặc định - Account={conf.get('Account')}")

    logger.info(f"🔍 DEBUG: conf sau khi thay đổi - Account={conf.get('Account')}, ACpass={conf.get('ACpass')}")

    # Chỉ gán provider nếu là xuất thật
    if not preview:
        log.provider = provider.lower()

    if provider == "VNPT":
        return handle_vnpt(log, preview, custom_data, conf, from_auto=from_auto)
    elif provider == "MOBIFONE":
        return handle_mobifone(log, preview, custom_data, conf)
    elif provider == "VIETTEL":
        return handle_viettel(log, preview, custom_data, conf)
    elif provider == "MISA":
        return handle_misa(log, preview, custom_data, conf)
    elif provider == "EASY":
        return handle_easy(log, preview, custom_data, conf)
    elif provider == "BKAV":
        return handle_bkav(log, preview, custom_data, conf)
    elif provider == "FAST":
        return handle_fast(log, preview, custom_data, conf)
    elif provider == "HILO":
        return handle_hilo(log, preview, custom_data, conf)
    elif provider == "MOBIFONE_TEST":
        return handle_mobifone_test(log, preview, custom_data, conf)
    else:
        return {"success": False, "message": f"Chưa hỗ trợ hóa đơn: {provider}"}

def send_invoice_from_log_with_provider(log, provider, preview=False, custom_data=None, api_account=None, api_pass=None, from_auto=False):
    """Gửi hóa đơn với provider cụ thể (dùng cho preview từ frontend)"""
    config = load_config()

    provider = provider.upper()
    conf = config.get(provider, {})

    if not conf:
        return {"success": False, "message": f"Không tìm thấy cấu hình cho provider {provider}"}

    # Nếu có api_account và api_pass từ user, ưu tiên sử dụng chúng
    if api_account and api_pass:
        conf["Account"] = api_account
        conf["ACpass"] = api_pass

    # Chỉ gán provider nếu là xuất thật
    if not preview:
        log.provider = provider.lower()

    # Ghi log để debug
    logger.info(f"🔥 Provider (specific): {provider}")
    logger.info(f"🔥 Config keys: {list(config.keys())}")
    logger.info(f"🔥 Conf: {conf}")

    if provider == "VNPT":
        return handle_vnpt(log, preview, custom_data, conf, from_auto=from_auto)
    elif provider == "MOBIFONE":
        return handle_mobifone(log, preview, custom_data, conf)
    elif provider == "VIETTEL":
        return handle_viettel(log, preview, custom_data, conf)
    elif provider == "MISA":
        return handle_misa(log, preview, custom_data, conf)
    elif provider == "EASY":
        return handle_easy(log, preview, custom_data, conf)
    elif provider == "BKAV":
        return handle_bkav(log, preview, custom_data, conf)
    elif provider == "FAST":
        return handle_fast(log, preview, custom_data, conf)
    elif provider == "HILO":
        return handle_hilo(log, preview, custom_data, conf)
    elif provider == "MOBIFONE_TEST":
        return handle_mobifone_test(log, preview, custom_data, conf)
    else:
        return {"success": False, "message": f"Chưa hỗ trợ hóa đơn: {provider}"}

#========== VNPT ======================================================================================================================================
def handle_vnpt(log, preview, custom_data, conf, from_auto=False):
    from app import db
    from app import app
    from sqlalchemy import text

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')
    date_folder = today.strftime('%Y%m%d')

    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()

    raw_thue = str(custom_data.get("thueStr", custom_data.get("thue", "10"))).strip()
    
    logger.info(f"🧪 VNPT – Thuế truyền vào: {raw_thue} | fuelType = {log.fuelType}")

    if raw_thue == "KKKNT":
        thue = 0
        thue_suat_gui = -2
    elif raw_thue == "KCT":
        thue = 0
        thue_suat_gui = -1
    else:
        try:
            thue = float(raw_thue)
            thue_suat_gui = str(int(thue))
        except:
            thue = 10
            thue_suat_gui = 10

    mkhang = custom_data.get("mkhang", "").strip()
    thhdv = map_fuel_name(log.fuelType)
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()
    phone = custom_data.get("phone", "").strip()
    cccd = custom_data.get("cccd", "").strip()
    mdvqhns = custom_data.get("mdvqhns", "").strip() 

    # === Tính toán lại giống frontend ===
    sl = log.transactionAmount / 1000
    price_vat = log.transactionPrice or 0
    cost_vat = log.transactionCost or 0

    discount_per_lit_vat = float(custom_data.get("discountPerLitVat", 0) or 0)

    if discount_per_lit_vat > 0:
        discount_per_lit = round(discount_per_lit_vat / (1 + thue / 100), 4)
        discount_total = round(sl * discount_per_lit)
    else:
        discount_per_lit = 0
        discount_total = 0

    # === Khởi tạo biến ===
    dg_chua_vat = 0
    tt_chua_vat = 0
    tien_thue = 0
    tong_cong = 0
    discount_per_lit = 0
    discount_total = 0
    congtienhang = 0

    if discount_per_lit_vat > 0:
        discount_per_lit = round(discount_per_lit_vat / (1 + thue / 100), 4)
        discount_total = round(sl * discount_per_lit)

        dg_chua_vat = round(price_vat / (1 + thue / 100), 4)
        tt_chua_vat = round(cost_vat / (1 + thue / 100))

        congtienhang = tt_chua_vat - discount_total
        tien_thue = round(congtienhang * thue / 100)
        tong_cong = congtienhang + tien_thue
    else:
        dg_chua_vat = round(price_vat / (1 + thue / 100), 4)
        tt_chua_vat = round(cost_vat / (1 + thue / 100))
        congtienhang = tt_chua_vat
        tong_cong = cost_vat
        tien_thue = round(cost_vat - tt_chua_vat)

    cost_text = num2words(round(tong_cong), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    logger.info(
        f"🧪 Thông tin thuế tạo XML: "
        f"Thuế suất = {str(int(thue_suat_gui))}, "
        f"Nhiên liệu = {thhdv}, "  
        f"Tiền thuế = {tien_thue}, "            
        f"Tổng tiền sau thuế = {tong_cong:.0f}"
    )
    
    # === XML hàng hóa ===
    hhdvu_xml = f'''
    <DSHHDVu>
      <HHDVu>
        <TChat>1</TChat>
        <STT>1</STT>
        <MHHDVu>{escape(log.fuelType)}</MHHDVu>
        <THHDVu>{thhdv}</THHDVu>
        <DVTinh>Lít</DVTinh>
        <SLuong>{sl:.3f}</SLuong>
        <DGia>{dg_chua_vat:.4f}</DGia>
        <ThTien>{round(tt_chua_vat)}</ThTien>
        <TSuat>{thue_suat_gui}</TSuat>
        <TThue>0</TThue>
        <TSThue>{round(tt_chua_vat)}</TSThue>
      </HHDVu>'''

    if discount_total > 0:
        hhdvu_xml += f'''
      <HHDVu>
        <TChat>3</TChat>
        <STT>2</STT>
        <MHHDVu>CKTM</MHHDVu>
        <THHDVu>Chiết khấu thương mại</THHDVu>
        <DVTinh>Lít</DVTinh>
        <SLuong>{sl:.3f}</SLuong>
        <DGia>{discount_per_lit:.4f}</DGia>
        <ThTien>{round(discount_total)}</ThTien>
        <TSuat>{thue_suat_gui}</TSuat>
        <TThue>0</TThue>
        <TSThue>{round(discount_total)}</TSThue>
      </HHDVu>'''

    hhdvu_xml += '</DSHHDVu>'

    # === XML đầy đủ ===
    xml_content = f'''<DSHDon>
      <HDon>
        <key>{log.transactionCode}</key>
        <DLHDon>
          <TTChung>
            <NLap>{today_str}</NLap>
            <DVTTe>VND</DVTTe>
            <TGia>1</TGia>
            {"<HTTToan>" + escape(httt) + "</HTTToan>" if httt else ""}
            <TTKhac>
                <TTin>
                  <TTruong>Extra</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra test</DLieu>
                </TTin>
                <TTin>
                  <TTruong>Extra1</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra1 test</DLieu>
                </TTin>
                <TTin>
                  <TTruong>Extra2</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>Extra2 test</DLieu>
                </TTin>
            </TTKhac>
          </TTChung>
          <NDHDon>
            <NBan>
              <TTKhac>
                <TTin>
                  <TTruong>Extra3</TTruong>
                  <KDLieu>string</KDLieu>
                  <DLieu>{escape(plate)}</DLieu>
                </TTin>
              </TTKhac>
            </NBan>
            <NMua>
              {"<HVTNMHang>" + escape(ho_ten) + "</HVTNMHang>" if ho_ten else ""}
              {"<Ten>" + escape(ten) + "</Ten>" if ten else ""}
              {"<MST>" + escape(mst) + "</MST>" if mst else ""}
              {"<DChi>" + escape(dchi) + "</DChi>" if dchi else ""}
              {"<MKHang>" + escape(mkhang) + "</MKHang>" if mkhang else ""}
              <DCTDTu>{escape(email)}</DCTDTu>
              <SDThoai>{escape(phone)}</SDThoai>
              <CCCDan>{escape(cccd)}</CCCDan>
              <TTKhac>
                <TTin>
                  <TTruong>Extra6</TTruong>
                  <KDLieu>string</KDLieu>
                  {"<DLieu>" + escape(mkhang) + "</DLieu>" if mkhang else ""}
                </TTin>
              </TTKhac>
            </NMua>
            {hhdvu_xml}
            <TToan>
              <THTTLTSuat>
                <LTSuat>
                  <TSuat>{thue_suat_gui}</TSuat>
                  <TThue>{tien_thue}</TThue>
                  <ThTien>{round(congtienhang)}</ThTien>
                </LTSuat>
              </THTTLTSuat>
              <TgTCThue>{round(congtienhang)}</TgTCThue>
              <TgTThue>{tien_thue}</TgTThue>
              <TTCKTMai>{round(discount_total)}</TTCKTMai>
              <TgTTTBSo>{tong_cong}</TgTTTBSo>
              <TgTTTBChu>{cost_text}</TgTTTBChu>
            </TToan>
          </NDHDon>
        </DLHDon>
      </HDon>
    </DSHDon>'''

    # === Trả kết quả nếu preview ===
    if preview:
        return {
            "success": True,
            "xml": xml_content.strip(),
            "sl": round(sl, 3),
            "dgChuaVat": round(dg_chua_vat, 4),
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": tien_thue,
            "ttVat": tong_cong,
            "chu": cost_text,
            "ho_ten": ho_ten,
            "ten": ten,
            "mst": mst,
            "dchi": dchi,
            "httt": httt,
            "thue": thue_suat_gui,
            "mkhang": mkhang,
            "thhdv": thhdv,
            "fuelType": log.fuelType,
            "discountPerLit": round(discount_per_lit, 4),
            "discountTotal": round(discount_total),
            "congtienhang": round(congtienhang)
        }

    # Gửi hóa đơn qua SOAP
    soap_xml = f'''<?xml version="1.0" encoding="utf-8"?>
    <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                     xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
      <soap12:Body>
        <ImportAndPublishInvMTT xmlns="http://tempuri.org/">
          <Account>{conf["Account"]}</Account>
          <ACpass>{conf["ACpass"]}</ACpass>
          <xmlInvData><![CDATA[{xml_content}]]></xmlInvData>
          <username>{conf["username"]}</username>
          <password>{conf["password"]}</password>
          <pattern>{conf["pattern"]}</pattern>
          <serial>{conf["serial"]}</serial>
          <convert>{conf["convert"]}</convert>
        </ImportAndPublishInvMTT>
      </soap12:Body>
    </soap12:Envelope>'''

    url = conf["link_api"]
    headers = {"Content-Type": "application/soap+xml; charset=utf-8"}
    response = requests.post(url, data=soap_xml.encode("utf-8"), headers=headers)
    vnpt_response = response.text.strip()

    # Ghi file debug
    debug_dir = os.path.join("debug", date_folder)
    os.makedirs(debug_dir, exist_ok=True)

    # Xử lý phản hồi từ VNPT
    shdon = None
    is_success = False

    # Kiểm tra phản hồi OK
    match = re.search(r"OK:.*?-(\d{10,20})</ImportAndPublishInvMTTResult>", vnpt_response)
    if match:
        shdon = match.group(1)
        is_success = True
        logger.info(f"🔍 Số hóa đơn trích được từ OK: {shdon}")
    # Xử lý ERR:13
    elif "ERR:13" in vnpt_response:
        logger.info("🧠  Nhận được ERR:13, tiến hành gọi API listInvByCusFkey")

        # === Hàm tìm kiếm hóa đơn theo fkey ===
        def search_invoice_by_fkey(config, fkey):
            try:
                # Tạo SOAP Envelope
                soap_xml = f'''<?xml version="1.0" encoding="utf-8"?>
                <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                                 xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                                 xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
                  <soap12:Body>
                    <listInvByCusFkey xmlns="http://tempuri.org/">
                      <key>{fkey}</key>
                      <userName>{config["username"]}</userName>
                      <userPass>{config["password"]}</userPass>
                    </listInvByCusFkey>
                  </soap12:Body>
                </soap12:Envelope>'''

                # Thiết lập header cho yêu cầu SOAP
                headers = {
                    'Content-Type': 'application/soap+xml; charset=utf-8',
                    'SOAPAction': 'http://tempuri.org/listInvByCusFkey'
                }

                # Gửi yêu cầu POST
                response = requests.post(config["link_api_fkey"], data=soap_xml.encode('utf-8'), headers=headers)
                logger.info(f"🧠 Response Status Code for listInvByCusFkey: {response.status_code}")
                logger.info(f"🧠 Response Content for listInvByCusFkey: {response.text}")

                # Kiểm tra trạng thái phản hồi
                if response.status_code != 200:
                    logger.error(f"❌ Yêu cầu API thất bại với mã trạng thái: {response.status_code}")
                    return None

                # Phân tích XML phản hồi
                root = ET.fromstring(response.text)
                result = root.find('.//{http://tempuri.org/}listInvByCusFkeyResult').text

                # Kiểm tra lỗi
                if result.startswith("ERR:"):
                    logger.error(f"❌ API trả về lỗi: {result}")
                    return None

                # Phân tích chuỗi XML trả về
                data_root = ET.fromstring(result)
                invoices = data_root.findall('.//Item')

                if not invoices:
                    logger.info("⚠ Không tìm thấy hóa đơn nào.")
                    return None

                # Lấy số hóa đơn từ hóa đơn đầu tiên
                inv_num = invoices[0].find('invNum').text if invoices[0].find('invNum') is not None else None
                if inv_num:
                    logger.info(f"🔍 Tìm thấy số hóa đơn: {inv_num}")
                    return inv_num
                else:
                    logger.info("⚠ Không có invNum trong phản hồi.")
                    return None

            except Exception as e:
                logger.error(f"Đã xảy ra lỗi khi gọi API listInvByCusFkey: {str(e)}")
                return None

        inv_num = search_invoice_by_fkey(conf, log.transactionCode)
        if inv_num:
            # Điều chỉnh inv_num để có ít nhất 10 chữ số
            if len(inv_num) < 10:
                inv_num = inv_num.zfill(11)  # Thêm số 0 vào đầu nếu cần
            shdon = inv_num
            is_success = True
            # Tạo phản hồi SOAP envelope đầy đủ để regex có thể trích xuất
            vnpt_response = f'''<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <soap:Body>
        <ImportAndPublishInvMTTResponse xmlns="http://tempuri.org/">
          <ImportAndPublishInvMTTResult>OK:1/002;C25MAT-{log.transactionCode}-M1-25-22786-{shdon}</ImportAndPublishInvMTTResult>
        </ImportAndPublishInvMTTResponse>
      </soap:Body>
    </soap:Envelope>'''
            logger.info(f"🔍 Tìm thấy số hóa đơn từ API: {shdon}")
        else:
            logger.info("⚠ Không tìm thấy hóa đơn cho fkey này.")
    else:
        logger.info("🧠 Phản hồi không xác định hoặc lỗi khác.")

    filename = f"{shdon}_{log.transactionCode}_vnpt.txt" if is_success else f"{log.transactionCode}_vnpt_ERR.txt"

    full_file_path = os.path.join(debug_dir, filename)

    # Ghi SOAP + XML + Response vào file
    pretty_soap = re.sub(
        "<xmlInvData>.*?</xmlInvData>",
        "<xmlInvData><></xmlInvData>",
        soap_xml,
        flags=re.DOTALL
    )

    with open(full_file_path, "w", encoding="utf-8") as f:
        f.write("SOAP:\n")
        f.write(pretty_soap.strip() + "\n\n")
        f.write("XML:\n")
        f.write(html.unescape(xml_content.strip()) + "\n\n")
        f.write("VNPT Response:\n")
        f.write(html.unescape(vnpt_response))

    # Nếu thành công → lưu vào DB và trả kết quả phù hợp
    if is_success:
        log.invoiceNumber = shdon
        log.verificationCode = shdon
        log.isInvoiced = True
        db.session.commit()
        
        # Ghi vào bảng invoice_log
        try:
            sql = text("""
                INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                VALUES (:transaction_code, :invoice_number, :custom_data)
            """)
            with app.app_context():
                db.session.execute(sql, {
                    "transaction_code": log.transactionCode,
                    "invoice_number": shdon,
                    "custom_data": json.dumps(custom_data, ensure_ascii=False)
                })
                db.session.commit()
            logger.info(f"✅ Đã lưu invoice_log cho giao dịch {log.transactionCode}")
        except Exception as e:
            logger.error(f"❌ Lỗi khi lưu invoice_log: {e}")
        
        logger.info(f"✅ Số hóa đơn là: {shdon}")
        if from_auto:
            logger.info("✅ TRẢ DICT CHO BACKEND (from_auto=True)")
            return {
                "success": True,
                "invoiceNumber": shdon,
                "message": "Đã gửi hóa đơn thành công",
                "response": vnpt_response
            }
        else:
            logger.info("✅ TRẢ STRING CHO FRONTEND (from_auto=False)")
            return vnpt_response
    else:
        # Nếu không thành công
        if from_auto:
            logger.info("❌ TRẢ LỖI CHO BACKEND (from_auto=True)")
            return {
                "success": False,
                "invoiceNumber": None,
                "message": f"Lỗi từ VNPT: {vnpt_response}" if vnpt_response else "Không có phản hồi từ VNPT",
                "response": vnpt_response or "Không có phản hồi từ VNPT"
            }
        else:
            logger.info("❌ TRẢ STRING LỖI CHO FRONTEND (from_auto=False)")
            return vnpt_response or "Không có phản hồi từ VNPT"

# Xuất gộp cho vnpt
def create_invoice_vnpt_from_preview(preview_data, custom_data):
    import json, os, time, datetime
    from flask import current_app
    from app import logger, app, db, Log
    from send_invoice import handle_vnpt
    from sqlalchemy import text
    
    logger.info(f"🔥 DEBUG preview_data nhận được: {json.dumps(preview_data, ensure_ascii=False)}")

    # Tạo log giả từ dict preview_data
    class PreviewLog:
        def __init__(self, d):
            self.transactionCode = d.get("transactionCode") or "GOP_" + str(int(time.time()))
            self.transactionAmount = d.get("transactionAmount")
            self.transactionCost = d.get("transactionCost")
            self.transactionPrice = d.get("transactionPrice")
            self.fuelType = d.get("fuelType")
            self.transactionDate = d.get("transactionDate") or d.get("nl") or datetime.date.today().isoformat()
            self.invoiceNumber = None
            self.invoiceId = None
            self.isInvoiced = False
            self.verificationCode = None
            self.provider = "vnpt"

    log = PreviewLog(preview_data)

    # Lấy config vnpt
    config_path = os.path.join("config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    conf = config.get("VNPT") or {}
    
    # Nếu custom_data là string JSON thì parse lại thành dict
    try:
        if isinstance(custom_data, str):
            logger.info("🔍 custom_data là string – cố gắng parse JSON")
            custom_data = json.loads(custom_data)
        elif not isinstance(custom_data, dict):
            logger.warning(f"⚠️ custom_data không phải dict: {type(custom_data)} – ép thành dict rỗng")
            custom_data = {}
    except Exception as e:
        logger.warning(f"❌ Không thể parse custom_data: {e}")
        custom_data = {}

        
    result = handle_vnpt(log, preview=False, custom_data=custom_data, conf=conf)

    # Nếu là string, parse lại để lấy số HĐ nếu có
    if isinstance(result, str):
        logger.warning("⚠️ handle_vnpt trả về chuỗi – sẽ cố trích số hóa đơn")
        import re
        match = re.search(r"OK:.*?-(\d{10,20})", result)
        invoice_number = match.group(1) if match else None
        result = {
            "success": bool(invoice_number),
            "invoiceNumber": invoice_number,
            "response": result
        }

    # Nếu lỗi thì return luôn, KHÔNG gán invoiceNumber = transactionCode
    if not result.get("success"):
        logger.warning("❌ Gửi hóa đơn VNPT thất bại – không tiếp tục xử lý log gộp")
        return result

    # Nếu thành công thì tiếp tục gán các field chuẩn
    result["invoiceNumber"] = result.get("invoiceNumber") or result.get("shdon")
    result["invoiceId"] = result.get("invoiceId") or result.get("hdon_id")
    result["provider"] = "vnpt"

    
    # Gán lại số HĐ cho các log thật đã gộp
    try:
        gop_codes = preview_data.get("gopFromCodes", [])
        shdon = result["invoiceNumber"]

        logger.info(f"🧾 Danh sách gopFromCodes nhận được: {gop_codes}")
        logger.info(f"✅ Số hóa đơn cần gán: {shdon}")

        if shdon and gop_codes:
            with app.app_context():
                logs = db.session.query(Log).filter(Log.transactionCode.in_(gop_codes)).all()
                logger.info(f"🔍 Tìm thấy {len(logs)} log khớp trong DB để gán số hóa đơn.")
                for l in logs:
                    logger.info(f"✅ Gán số HĐ {shdon} cho log: {l.transactionCode}")
                    l.invoiceNumber = shdon
                    l.verificationCode = shdon
                    l.isInvoiced = True
                    l.provider = "vnpt"
                db.session.commit()
                
                #Lưu thông tin KH
                try:
                    for l in logs:
                        sql = text("""
                            INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                            VALUES (:transaction_code, :invoice_number, :custom_data)
                            ON DUPLICATE KEY UPDATE
                                invoice_number = VALUES(invoice_number),
                                custom_data = VALUES(custom_data)
                        """)
                        db.session.execute(sql, {
                            "transaction_code": l.transactionCode,
                            "invoice_number": shdon,
                            "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                        })
                    db.session.commit()
                    logger.info("✅ Đã lưu custom_data vào invoice_log cho các log gộp VNPT")
                except Exception as e:
                    logger.warning(f"❌ Lỗi khi lưu custom_data VNPT: {e}")
                    
                    
                logger.info(f"✅ Đã gán số HĐ {shdon} cho các log gốc: {gop_codes}")
        else:
            logger.warning("⚠️ Không có gopFromCodes hoặc shdon rỗng – bỏ qua cập nhật log gốc.")

    except Exception as e:
        logger.warning(f"❌ Không thể gán số HĐ vào log gốc: {e}")

    return result
    
#========== MOBIFONE ======================================================================================================================================
# Hóa đơn GTGT có mã tạo từ máy tính tiền
# Có số hóa đơn, nếu có HSM thì ký, không có thì bỏ qua
def handle_mobifone(log, preview, custom_data, conf):
    from app import db
    from app import app
    from sqlalchemy import text

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')

    # CHẶN GỬI LẠI nếu log đã có hóa đơn
    if not preview:
        if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() not in ["", "CHUA_RO_SOHĐ"]):
            return {
                "success": False,
                "message": f"Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}"
            }

    cost_text = num2words(round(log.transactionCost), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    # Dữ liệu người mua
    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()
    phone = custom_data.get("phone", "").strip()
    cccd = custom_data.get("cccd", "").strip()
    mdvqhns = custom_data.get("mdvqhns", "").strip()     

    raw_thue = custom_data.get("thue", 10)
    logger.info(f"🧪 Mobifone – Thuế truyền vào: {raw_thue} | fuelType = {log.fuelType}")
    try:
        thue = int(float(raw_thue))
    except (TypeError, ValueError):
        thue = 0

    # Tính toán dữ liệu hóa đơn
    thhdv = map_fuel_name(log.fuelType)
    sl = round(log.transactionAmount / 1000, 4)
    dg_chua_vat = log.transactionPrice / (1 + thue / 100)
    tt_chua_vat = log.transactionCost / (1 + thue / 100)
    tong_cong = log.transactionCost
    tien_thue = tong_cong - tt_chua_vat

    try:
        # Gọi API lấy token
        token_res = requests.post(conf["login_url"], json={
            "username": conf["username"],
            "password": conf["password"]
        }, timeout=10)

        logger.info("📥 Token response: " + token_res.text)
        token_json = token_res.json()

        # Lấy token và ma_dvcs rõ ràng
        token = token_json.get("token") or token_json.get("access_token")
        ma_dvcs = token_json.get("ma_dvcs") or conf.get("ma_dvcs")

        if not token or not ma_dvcs:
            return {"success": False, "message": f"Không lấy được token Mobifone.\nChi tiết: {token_res.text}"}

        # Gọi API lấy mẫu số
        cctbao_id = None
        try:
            get_url = conf.get("get_cctbao_url", "").strip()
            if get_url:
                full_url = f"{get_url}?refId=RF00059"
                headers = {
                    "Authorization": f"Bear {token};{ma_dvcs}",
                }
                logger.info("📤 Gọi API URL: " + full_url)
                res = requests.get(full_url, headers=headers, timeout=10)
                if res.ok:
                    data = res.json()
                    logger.info(f"📥 Mobifone trả {len(data)} mẫu số")
                    preferred_template = conf.get("template_code", "").strip()
                    found_template = None

                    for item in data:
                        if item.get("value") == preferred_template:
                            cctbao_id = item.get("id") or item.get("qlkhsdung_id")
                            found_template = f"{item.get('value')} - ID: {cctbao_id}"
                            break

                    if found_template:
                        logger.info(f"✅ Mẫu được chọn: {found_template}")
                    else:
                        logger.info(f"⚠️ Không tìm thấy mẫu phù hợp với template_code: {preferred_template}")

                    if data:
                        for item in data:
                            if item.get("value") == preferred_template:
                                cctbao_id = item.get("id") or item.get("qlkhsdung_id")
                                logger.info(f"✅ Đã chọn mẫu số: {item.get('value')} - ID: {cctbao_id} - KH: {item.get('khhdon')}")
                                break

        except Exception as ex:
            logger.info(f"❌ Lỗi khi gọi API mẫu số Mobifone: {ex}")

        # Fallback nếu không có mẫu số từ API
        if not cctbao_id:
            logger.info("⚠️ API không trả mẫu số – fallback config.json")
            cctbao_id = conf.get("cctbao_id")

        # Không có gì cả → lỗi
        if not cctbao_id:
            return {"success": False, "message": "Không tìm được mẫu số hóa đơn để tạo"}

        logger.info(
            f"🧪 Thông tin thuế tạo payload: "
            f"Thuế suất = {thue}, "
            f"Nhiên liệu = {thhdv}, "  
            f"Tiền thuế = {tien_thue}, "            
            f"Tổng tiền sau thuế = {tong_cong:.0f}"
        )
        # Tạo payload
        payload = {
            "editmode": 1,
            "tax_code": conf["tax_code"],
            "data": [{
                "cctbao_id": cctbao_id,
                "nlap": today_str,
                "fkey": log.transactionCode,
                "dvtte": "VND",
                "tgia": 1,
                "htttoan": httt,
                "mnmua": "",
                "mst": mst,
                "tnmua": ten,
                "email": email,
                "ten": ho_ten,
                "dchi": dchi,
                "sdtnmua": phone,
                "cmndmua": cccd,
                "tgtcthue": round(tt_chua_vat),
                "tgtthue": round(tien_thue),
                "tgtttbso": round(tong_cong),
                "tgtttbso_last": round(tong_cong),
                "tkcktmn": 0.0,
                "ttcktmai": 0.0,
                "tgtphi": 0.0,
                "mdvi": ma_dvcs,
                "tthdon": 0,
                "is_hdcma": 1,
                "details": [{
                    "data": [{
                        "id": 0,
                        "stt": 1,
                        "ma": log.fuelType,
                        "ten": thhdv,
                        "mdvtinh": "Lít",
                        "dgia": f"{dg_chua_vat:.4f}",
                        "kmai": "1",
                        "tsuat": thue,
                        "thtien": str(round(tt_chua_vat)),
                        "sluong": f"{sl:.3f}",
                        "tlckhau": 0,
                        "stckhau": 0,
                        "tthue": str(round(tien_thue)),
                        "tgtien": str(round(tong_cong))
                    }]
                }],
                "hoadon68_phi": [{
                    "data": [{
                        "tienphi": 0,
                        "tnphi": "Phí môi trường"
                    }]
                }],
                "hoadon68_khac": [{
                    "data": [{
                        "dlieu": "Địa chỉ",
                        "kdlieu": "decimal",
                        "ttruong": "dclhe"
                    }]
                }]
            }]
        }

        if preview:
            return {
                "success": True,
                "json": payload,
                "nlap": today_str,
                "sl": f"{sl:.3f}",
                "dgChuaVat": f"{dg_chua_vat:.4f}",
                "ttChuaVat": round(tt_chua_vat),
                "tienThue": round(tien_thue),
                "ttVat": round(tong_cong),
                "chu": cost_text,
                "thhdv": thhdv,
                "fuelType": log.fuelType
            }

        # Gửi tạo hóa đơn và sinh số
        result = handle_mobifone_submit(log, payload, conf, token, ma_dvcs, custom_data=custom_data)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType
        })
        return result

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn MOBIFONE: {str(e)}"}

# Nếu chờ ký, ký sau, chưa có số hóa đơn thì tạm gán mã GD lưu vào db, trên index là Fkey
def handle_mobifone_submit(log, payload, conf, token, ma_dvcs, custom_data=None):
    from app import db
    from app import app
    from sqlalchemy import text

    try:
        headers = {
            "Authorization": f"Bear {token};{ma_dvcs}",
            "Content-Type": "application/json"
        }

        # Gửi API SaveAndSignHoadon78
        res = requests.post(conf["link_api"], json=payload, headers=headers)

        if not res.ok:
            logger.info(f"❌ Status code: {res.status_code}")
            return {"success": False, "message": f"Lỗi gọi SaveAndSignHoadon78: {res.status_code} - {res.text}"}

        res_json = res.json()

        # Kiểm tra lỗi từ Mobifone
        if isinstance(res_json, list) and len(res_json) > 0 and "error" in res_json[0]:
            error_msg = res_json[0]["error"]
            if error_msg == "Mã fkey đã tồn tại!":
                # Lấy link_api_fkey từ cấu hình
                link_api_fkey = conf.get("link_api_fkey")
                if not link_api_fkey:
                    return {
                        "success": False,
                        "message": "Không có link_api_fkey trong cấu hình."
                    }

                # Chuẩn bị body cho API link_api_fkey
                fkey_payload = {"hdon_id": log.transactionCode}  # Sử dụng log.transactionCode làm fkey
                fkey_res = requests.post(link_api_fkey, json=fkey_payload, headers=headers)
                fkey_json = fkey_res.json()

                # Xử lý phản hồi từ link_api_fkey
                if fkey_res.ok and "ok" in fkey_json and fkey_json["ok"] == "OK":
                    data = fkey_json["data"][0]
                    hdon_id = data["hdon_id"]
                    shdon = data["shdon"]
                    tthai = data["tthai"]

                    # Cập nhật Log giống như trường hợp thành công
                    log.invoiceId = hdon_id
                    log.invoiceNumber = shdon
                    log.verificationCode = shdon
                    log.isInvoiced = True
                    log.provider = "mobifone"
                    db.session.commit()

                    # Trả về kết quả với thông tin hóa đơn
                    return {
                        "success": True,
                        "invoiceNumber": shdon,
                        "message": f"Hóa đơn đã tồn tại với trạng thái: {tthai}"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Lỗi khi gọi link_api_fkey: {fkey_json.get('error', 'Không rõ')} - Liên hệ Mobifone để được hỗ trợ"
                    }
            else:
                # Xử lý các lỗi khác từ Mobifone
                return {
                    "success": False,
                    "message": f"{error_msg} - Liên hệ Mobifone để được hỗ trợ"
                }

        # Xử lý phản hồi thành công từ Mobifone (nếu không có lỗi)
        res0 = res_json[0] if isinstance(res_json, list) and len(res_json) > 0 else {}
        data_raw = res0.get("data", {})

        if isinstance(data_raw, list) and len(data_raw) > 0:
            data = data_raw[0]
        elif isinstance(data_raw, dict):
            data = data_raw
        else:
            logger.warning("❌ Mobifone trả về data không hợp lệ")
            return {"success": False, "message": "Mobifone trả về dữ liệu không hợp lệ"}

        # Trích xuất hdon_id
        hdon_id = data.get("hdon_id")
        if not hdon_id:
            logger.warning("❌ Mobifone không trả về hdon_id hợp lệ! KHÔNG được fallback ID_...")
            return {"success": False, "message": "Mobifone không trả về mã hdon_id hợp lệ."}

        shdon = data.get("shdon") or log.transactionCode
        tthai = data.get("tthai") or res0.get("tthai", "")

        # Ghi lại log file
        date_folder = datetime.now().strftime('%Y%m%d')
        debug_dir = os.path.join("debug", date_folder)
        os.makedirs(debug_dir, exist_ok=True)

        # Tùy theo có shdon hay không mà đổi tên file
        if shdon:
            log_filename = f"{shdon}_{log.transactionCode}_mobifone.txt"
        else:
            log_filename = f"{log.transactionCode}_mobifone_err.txt"


        with open(os.path.join(debug_dir, log_filename), "w", encoding="utf-8") as f:
            json.dump({
                "username": conf.get("username", ""),
                "password": conf.get("password", ""),
                "token": token,
                "ma_dvcs": ma_dvcs,
                "payload": payload,
                "response_create": res_json
            }, f, ensure_ascii=False, indent=2)

        # Xử lý thành công
        if tthai in ["Đã ký", "Đã gửi"]:
            log.invoiceId = hdon_id
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.status = "OK"
            log.provider = "mobifone"
            log.isInvoiced = True
            db.session.commit()
        
        # Ghi vào bảng invoice_log            
            try:
                sql = text("""
                    INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                    VALUES (:transaction_code, :invoice_number, :custom_data)
                """)
                with app.app_context():
                    db.session.execute(sql, {
                        "transaction_code": log.transactionCode,
                        "invoice_number": shdon,
                        "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                    db.session.commit()
                logger.info(f"✅ Đã lưu invoice_log cho giao dịch {log.transactionCode}")
            except Exception as e:
                logger.error(f"❌ Lỗi khi lưu invoice_log: {e}")
                 
            logger.info(f"✅ Lưu vào DB: Số HĐ = {shdon}, Trạng thái = {tthai}")
            return {
                "success": True,
                "invoiceNumber": shdon,
                "message": f"Hóa đơn đã {tthai.lower()}"
            }

        # Trường hợp chưa ký
        logger.info(f"⚠️ Hóa đơn chưa ký: trạng thái = {tthai}")
        if shdon:
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.invoiceId = hdon_id
            log.isInvoiced = True
            log.provider = "mobifone"
            db.session.commit()
            
            # Ghi vào bảng invoice_log            
            try:
                sql = text("""
                    INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                    VALUES (:transaction_code, :invoice_number, :custom_data)
                """)
                with app.app_context():
                    db.session.execute(sql, {
                        "transaction_code": log.transactionCode,
                        "invoice_number": shdon,
                        "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                    db.session.commit()
                logger.info(f"✅ Đã lưu invoice_log cho giao dịch {log.transactionCode}")
            except Exception as e:
                logger.error(f"❌ Lỗi khi lưu invoice_log: {e}")
            
            
            logger.info(f"✅ Đã lưu HĐ (dù chưa ký): {shdon}")
        return {
            "success": True,
            "invoiceNumber": shdon,
            "message": f"Hóa đơn đã tạo nhưng chưa ký – Trạng thái: {tthai or 'N/A'}"
        }

    except Exception as e:
        logger.info(f"❌ Exception: {str(e)}")
        logger.info(traceback.format_exc())
        return {"success": False, "message": f"Lỗi khi gửi SaveAndSignHoadon78: {str(e)}"}

# Xuất gộp cho mobifone
def create_invoice_mobifone_from_preview(preview_data, custom_data):
    import json, os, time, datetime
    from flask import current_app
    from app import logger, app, db, Log
    from send_invoice import handle_mobifone
    from sqlalchemy import text

    logger.info(f"🔥 DEBUG preview_data nhận được: {json.dumps(preview_data, ensure_ascii=False)}")

    # Tạo log giả từ dict preview_data
    class PreviewLog:
        def __init__(self, d):
            self.transactionCode = d.get("transactionCode") or "GOP_" + str(int(time.time()))
            self.transactionAmount = d.get("transactionAmount")
            self.transactionCost = d.get("transactionCost")
            self.transactionPrice = d.get("transactionPrice")
            self.fuelType = d.get("fuelType")
            self.transactionDate = d.get("transactionDate") or d.get("nl") or datetime.date.today().isoformat()
            self.invoiceNumber = None
            self.invoiceId = None
            self.isInvoiced = False
            self.verificationCode = None
            self.provider = "mobifone"

    log = PreviewLog(preview_data)

    # Lấy config Mobifone
    config_path = os.path.join("config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    conf = config.get("MOBIFONE") or {}

    # Gọi hàm xử lý Mobifone chuẩn
    result = handle_mobifone(log, preview=False, custom_data=custom_data, conf=conf)

    # Nếu lỗi, không tiếp tục xử lý
    if isinstance(result, dict) and not result.get("success", False):
        logger.warning(f"❌ Gửi hóa đơn Mobifone thất bại – không tiếp tục gán số hóa đơn.")
        return result

    # Nếu trả về dạng string (trường hợp bất thường)
    if isinstance(result, str):
        logger.warning("⚠️ handle_mobifone trả về chuỗi – cố trích số hóa đơn")
        import re
        match = re.search(r"OK:.*?-(\d{10,20})", result)
        invoice_number = match.group(1) if match else None
        result = {
            "success": bool(invoice_number),
            "invoiceNumber": invoice_number,
            "response": result
        }
        if not result["success"]:
            logger.warning("❌ Chuỗi trả về từ Mobifone không chứa số hóa đơn – dừng.")
            return result

    # Nếu thành công → gán lại rõ ràng các field
    result["invoiceNumber"] = result.get("invoiceNumber") or result.get("shdon")
    result["invoiceId"] = result.get("invoiceId") or result.get("hdon_id")
    result["provider"] = "mobifone"

    
    # Gán lại số HĐ cho các log thật đã gộp
    try:
        gop_codes = preview_data.get("gopFromCodes", [])
        shdon = result["invoiceNumber"]

        logger.info(f"🧾 Danh sách gopFromCodes nhận được: {gop_codes}")
        logger.info(f"✅ Số hóa đơn cần gán: {shdon}")

        if shdon and gop_codes:
            with app.app_context():
                logs = db.session.query(Log).filter(Log.transactionCode.in_(gop_codes)).all()
                logger.info(f"🔍 Tìm thấy {len(logs)} log khớp trong DB để gán số hóa đơn.")
                for l in logs:
                    logger.info(f"✅ Gán số HĐ {shdon} cho log: {l.transactionCode}")
                    l.invoiceNumber = shdon
                    l.verificationCode = shdon
                    l.isInvoiced = True
                    l.provider = "mobifone"
                db.session.commit()
                
                #Lưu thông tin KH
                try:
                    for l in logs:
                        sql = text("""
                            INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                            VALUES (:transaction_code, :invoice_number, :custom_data)
                            ON DUPLICATE KEY UPDATE
                                invoice_number = VALUES(invoice_number),
                                custom_data = VALUES(custom_data)
                        """)
                        db.session.execute(sql, {
                            "transaction_code": l.transactionCode,
                            "invoice_number": shdon,
                            "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                        })
                    db.session.commit()
                    logger.info("✅ Đã lưu custom_data vào invoice_log cho các log gộp Mobifone")
                except Exception as e:
                    logger.warning(f"❌ Lỗi khi lưu custom_data Mobifone: {e}")

                logger.info(f"✅ Đã gán số HĐ {shdon} cho các log gốc: {gop_codes}")
        else:
            logger.warning("⚠️ Không có gopFromCodes hoặc shdon rỗng – bỏ qua cập nhật log gốc.")

    except Exception as e:
        logger.warning(f"❌ Không thể gán số HĐ vào log gốc: {e}")

    return result

#========== MOBIFONE_TEST ===============================================================================
# Hóa đơn GTGT không mã
# Có số hóa đơn test, chưa ký
def handle_mobifone_test(log, preview, custom_data, conf):
    from app import db
    from app import app
    from sqlalchemy import text

    today = datetime.now()
    today_str = today.strftime('%Y-%m-%d')

    # CHẶN GỬI LẠI nếu log đã có hóa đơn
    if not preview:
        if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() not in ["", "CHUA_RO_SOHĐ"]):
            return {
                "success": False,
                "message": f"Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}"
            }

    cost_text = num2words(round(log.transactionCost), lang='vi').replace("lẻ", "không trăm")
    cost_text = cost_text[0].upper() + cost_text[1:] + " đồng chẵn."

    # Dữ liệu người mua
    ho_ten = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    ten = custom_data.get("ten", "").strip()
    mst = custom_data.get("mst", "").strip()
    dchi = custom_data.get("dchi", "").strip()
    httt = custom_data.get("httt", "").strip()
    email = custom_data.get("email", "").strip()
    plate = custom_data.get("plate", "").strip()
    phone = custom_data.get("phone", "").strip()
    cccd = custom_data.get("cccd", "").strip()
    mdvqhns = custom_data.get("mdvqhns", "").strip() 

    raw_thue = custom_data.get("thue", 10)
    logger.info(f"🧪 Mobifone_test – Thuế truyền vào: {raw_thue} | fuelType = {log.fuelType}")
    
    try:
        thue = int(float(raw_thue))
    except (TypeError, ValueError):
        thue = 0

    # Tính toán dữ liệu hóa đơn
    thhdv = map_fuel_name(log.fuelType)
    sl = round(log.transactionAmount / 1000, 4)
    dg_chua_vat = log.transactionPrice / (1 + thue / 100)
    tt_chua_vat = log.transactionCost / (1 + thue / 100)
    tong_cong = log.transactionCost
    tien_thue = tong_cong - tt_chua_vat

    try:
        token_res = requests.post(conf["login_url"], json={
            "username": conf["username"],
            "password": conf["password"],
            "tax_code": conf["tax_code"]
        })
        token_json = token_res.json()
        token = token_json.get("token")
        ma_dvcs = token_json.get("ma_dvcs")

        if not token or not ma_dvcs:
            return {"success": False, "message": "Không lấy được token Mobifone."}
        
        logger.info(
            f"🧪 Thông tin thuế tạo payload: "
            f"Thuế suất = {thue},"
            f"Nhiên liệu = {thhdv}, "  
            f"Tiền thuế = {tien_thue}, "            
            f"Tổng tiền sau thuế = {tong_cong:.0f}"
        )
        # Tạo payload
        payload = {
            "editmode": 1,
            "tax_code": conf["tax_code"],
            "data": [{
                "cctbao_id": conf["cctbao_id"],
                "nlap": today_str,
                "dvtte": "VND",
                "tgia": 1,
                "htttoan": httt,
                "mnmua": "",
                "mst": mst,
                "tnmua": ten,
                "email": email,
                "ten": ho_ten,
                "dchi": dchi,
                "sdtnmua": phone,
                "cmndmua": cccd,
                "tgtcthue": round(tt_chua_vat),
                "tgtthue": round(tien_thue),
                "tgtttbso": round(tong_cong),
                "tgtttbso_last": round(tong_cong),
                "tkcktmn": 0.0,
                "ttcktmai": 0.0,
                "tgtphi": 0.0,
                "mdvi": ma_dvcs,
                "tthdon": 0,
                "is_hdcma": 1,
                "details": [{
                    "data": [{
                        "id": 0,
                        "stt": 1,
                        "ma": log.fuelType,
                        "ten": thhdv,
                        "mdvtinh": "Lít",
                        "dgia": f"{dg_chua_vat:.4f}",
                        "kmai": "1",
                        "tsuat": thue,
                        "thtien": str(round(tt_chua_vat)),
                        "sluong": str(sl),
                        "tlckhau": 0,
                        "stckhau": 0,
                        "tthue": str(round(tien_thue)),
                        "tgtien": str(round(tong_cong))
                    }]
                }],
                "hoadon68_phi": [{
                    "data": [{
                        "tienphi": 0,
                        "tnphi": "Phí môi trường"
                    }]
                }],
                "hoadon68_khac": [{
                    "data": [{
                        "dlieu": "Địa chỉ",
                        "kdlieu": "decimal",
                        "ttruong": "dclhe"
                    }]
                }]
            }]
        }

        if preview:
            return {
                "success": True,
                "json": payload,
                "nlap": today_str,
                "sl": sl,
                "dgChuaVat": f"{dg_chua_vat:.4f}",
                "ttChuaVat": round(tt_chua_vat),
                "tienThue": round(tien_thue),
                "ttVat": round(tong_cong),
                "chu": cost_text,
                "thhdv": thhdv,
                "fuelType": log.fuelType
            }

        # Gửi tạo hóa đơn và sinh số
        result = handle_mobifone_submit_test(log, payload, conf, token, ma_dvcs, custom_data=custom_data)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType
        })
        return result

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn MOBIFONE: {str(e)}"}

# Nếu chờ ký, có số hóa đơn thì lấy đúng số hóa đơn hiển thị trên index
def handle_mobifone_submit_test(log, payload, conf, token, ma_dvcs, custom_data=None):
    from app import db
    from app import app
    from sqlalchemy import text

    try:
        headers = {
            "Authorization": f"Bearer {token};{ma_dvcs}",
            "Content-Type": "application/json"
        }

        # Gửi tạo hóa đơn
        res = requests.post(conf["link_api"], json=payload, headers=headers)
        res_json = res.json()

        hdon_id = res_json[0]['data']['hdon_id'] if res_json and 'data' in res_json[0] else None

        # Duyệt hóa đơn
        approve_payload = {"data": [{"lsthdon_id": [hdon_id]}], "tax_code": None}

        # Gửi yêu cầu sinh số hóa đơn
        gen_res = requests.post(conf.get("generate_url"), json=approve_payload, headers=headers)
        gen_json = gen_res.json() if gen_res.ok else {}

        # Trích số hóa đơn trực tiếp từ res_json
        shdon = None
        try:
            if isinstance(res_json, list) and len(res_json) > 0:
                shdon = res_json[0].get("data", {}).get("shdon")
        except Exception as e:
            logger.info(f"❌ Không thể lấy shdon: {e}")

        # Ghi lại log file
        date_folder = datetime.now().strftime('%Y%m%d')
        debug_dir = os.path.join("debug", date_folder)
        os.makedirs(debug_dir, exist_ok=True)

        # Tùy theo có shdon hay không mà đổi tên file
        if shdon:
            log_filename = f"{shdon}_{log.transactionCode}_mobifone_test.txt"
        else:
            log_filename = f"{log.transactionCode}_test_err.txt"

        with open(os.path.join(debug_dir, log_filename), "w", encoding="utf-8") as f:
            json.dump({
                "username": conf.get("username", ""),
                "password": conf.get("password", ""),
                "token": token,
                "ma_dvcs": ma_dvcs,
                "payload": payload,
                "response_create": res_json
            }, f, ensure_ascii=False, indent=2)

        # Nếu có shdon thì cập nhật log vào DB
        if shdon:
            log.invoiceNumber = shdon
            log.verificationCode = shdon
            log.isInvoiced = True
            log.provider = "mobifone"
            log.invoiceId = hdon_id
            db.session.commit()
            
            # Ghi vào bảng invoice_log            
            try:
                sql = text("""
                    INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                    VALUES (:transaction_code, :invoice_number, :custom_data)
                """)
                with app.app_context():
                    db.session.execute(sql, {
                        "transaction_code": log.transactionCode,
                        "invoice_number": shdon,
                        "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                    db.session.commit()
                logger.info(f"✅ Đã lưu invoice_log cho giao dịch {log.transactionCode}")
            except Exception as e:
                logger.error(f"❌ Lỗi khi lưu invoice_log: {e}")
            
            logger.info(f"✅ Số hóa đơn đã lưu vào DB: {shdon}")
        else:
            logger.info("⚠️ Không tìm thấy số hóa đơn trong response.")

        return {
            "success": True,
            "hdon_id": hdon_id,
            "invoiceId": hdon_id,
            "shdon": shdon,
            "response": res_json
        }

    except Exception as e:
        return {"success": False, "message": f"❌ Lỗi khi gửi hóa đơn MOBIFONE_TEST: {str(e)}"}
        
# Xuất gộp cho mobifone_test
def create_invoice_mobifone_test_from_preview(preview_data, custom_data):
    import json, os, time, datetime
    from flask import current_app
    from app import logger, app, db, Log
    from send_invoice import handle_mobifone_test
    from sqlalchemy import text    

    logger.info(f"🔥 DEBUG preview_data nhận được: {json.dumps(preview_data, ensure_ascii=False)}")

    # Tạo log giả từ dict preview_data
    class PreviewLog:
        def __init__(self, d):
            self.transactionCode = d.get("transactionCode") or "GOP_" + str(int(time.time()))
            self.transactionAmount = d.get("transactionAmount")
            self.transactionCost = d.get("transactionCost")
            self.transactionPrice = d.get("transactionPrice")
            self.fuelType = d.get("fuelType")
            self.transactionDate = d.get("transactionDate") or d.get("nl") or datetime.date.today().isoformat()
            self.invoiceNumber = None
            self.invoiceId = None
            self.isInvoiced = False
            self.verificationCode = None
            self.provider = "mobifone_test"

    log = PreviewLog(preview_data)

    # Lấy config Mobifone
    config_path = os.path.join("config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    conf = config.get("MOBIFONE_TEST") or {}

    # Gọi hàm xử lý Mobifone chuẩn
    result = handle_mobifone(log, preview=False, custom_data=custom_data, conf=conf)

    # Nếu lỗi, không tiếp tục xử lý
    if isinstance(result, dict) and not result.get("success", False):
        logger.warning(f"❌ Gửi hóa đơn Mobifone thất bại – không tiếp tục gán số hóa đơn.")
        return result

    # Nếu trả về dạng string (trường hợp bất thường)
    if isinstance(result, str):
        logger.warning("⚠️ handle_mobifone trả về chuỗi – cố trích số hóa đơn")
        import re
        match = re.search(r"OK:.*?-(\d{10,20})", result)
        invoice_number = match.group(1) if match else None
        result = {
            "success": bool(invoice_number),
            "invoiceNumber": invoice_number,
            "response": result
        }
        if not result["success"]:
            logger.warning("❌ Chuỗi trả về từ Mobifone không chứa số hóa đơn – dừng.")
            return result

    # Nếu thành công → gán lại rõ ràng các field
    result["invoiceNumber"] = result.get("invoiceNumber") or result.get("shdon")
    result["invoiceId"] = result.get("invoiceId") or result.get("hdon_id")
    result["provider"] = "mobifone"

    
    # Gán lại số HĐ cho các log thật đã gộp
    try:
        gop_codes = preview_data.get("gopFromCodes", [])
        shdon = result["invoiceNumber"]

        logger.info(f"🧾 Danh sách gopFromCodes nhận được: {gop_codes}")
        logger.info(f"✅ Số hóa đơn cần gán: {shdon}")

        if shdon and gop_codes:
            with app.app_context():
                logs = db.session.query(Log).filter(Log.transactionCode.in_(gop_codes)).all()
                logger.info(f"🔍 Tìm thấy {len(logs)} log khớp trong DB để gán số hóa đơn.")
                for l in logs:
                    logger.info(f"✅ Gán số HĐ {shdon} cho log: {l.transactionCode}")
                    l.invoiceNumber = shdon
                    l.verificationCode = shdon
                    l.isInvoiced = True
                    l.provider = "mobifone_test"
                db.session.commit()
                                
                #Lưu thông tin KH
                try:
                    for l in logs:
                        sql = text("""
                            INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                            VALUES (:transaction_code, :invoice_number, :custom_data)
                            ON DUPLICATE KEY UPDATE
                                invoice_number = VALUES(invoice_number),
                                custom_data = VALUES(custom_data)
                        """)
                        db.session.execute(sql, {
                            "transaction_code": l.transactionCode,
                            "invoice_number": shdon,
                            "custom_data": json.dumps(custom_data or {}, ensure_ascii=False)
                        })
                    db.session.commit()
                    logger.info("✅ Đã lưu custom_data vào invoice_log cho các log gộp Mobifone_test")
                except Exception as e:
                    logger.warning(f"❌ Lỗi khi lưu custom_data Mobifone_test: {e}")
                    
                logger.info(f"✅ Đã gán số HĐ {shdon} cho các log gốc: {gop_codes}")
        else:
            logger.warning("⚠️ Không có gopFromCodes hoặc shdon rỗng – bỏ qua cập nhật log gốc.")

    except Exception as e:
        logger.warning(f"❌ Không thể gán số HĐ vào log gốc: {e}")

    return result

#========== BKAV ======================================================================================================================================
# Hóa đơn máy tính tiền
def handle_bkav(log, preview, custom_data, conf):
    from datetime import datetime
    import json, os
    from num2words import num2words
    import base64
    import requests
    from app import db, app
    from sqlalchemy import text
    import threading

    _bkav_processing_lock = threading.Lock()
    global _bkav_processing_transactions
    if "_bkav_processing_transactions" not in globals():
        _bkav_processing_transactions = set()

    # ==== HÀM PHỤ GOM VÀO ====
    def map_fuel_name(code):
        try:
            path = os.path.join(os.path.dirname(__file__), "config", "fuel.json")
            with open(path, "r", encoding="utf-8") as f:
                mapping = json.load(f)
            return mapping.get(code, code)
        except:
            return code

    def get_bkav_tax_rate_mapping(tax_rate):
        tax_mapping = {
            0: (1, 0), 5: (2, 5), 10: (3, 10), 8: (9, 8),
            -1: (4, -1), -2: (5, -2)
        }
        return tax_mapping.get(tax_rate, (9, 8))

    def get_bkav_payment_method_id(payment_method):
        payment_mapping = {
            "Tiền mặt": 1, "TM": 1,
            "Chuyển khoản": 2, "CK": 2,
            "TM/CK": 3, "Tiền mặt/Chuyển khoản": 3,
        }
        return payment_mapping.get(payment_method, 3)

    def validate_bkav_customer_data(custom_data):
        v = {}
        v["buyer_name"] = custom_data.get("ho_ten", "").strip()
        v["buyer_tax_code"] = custom_data.get("mst", "").strip()
        v["buyer_unit_name"] = custom_data.get("ten", "").strip()
        v["buyer_address"] = custom_data.get("dchi", "").strip()
        v["receiver_address"] = custom_data.get("receiver_address", v["buyer_address"]).strip()
        v["receiver_email"] = custom_data.get("email", "").strip()
        v["receiver_mobile"] = custom_data.get("phone", "").strip()
        v["receiver_name"] = custom_data.get("receiver_name", v["buyer_name"]).strip()
        cccd = custom_data.get("cccd", "").strip()
        v["cccd"] = cccd if cccd.isdigit() and len(cccd) == 12 else ""
        v["fiscal_codes"] = custom_data.get("mdvqhns", "").strip()
        plate = custom_data.get("plate", "").strip()
        v["user_define"] = json.dumps({"BienSoXe": plate}, ensure_ascii=False) if plate else ""
        v["payment_method_id"] = get_bkav_payment_method_id(custom_data.get("htttoan", "TM/CK"))
        return v

    def handle_bkav_submit(log, command_data, conf, custom_data=None, preview=False):
        try:
            json_string = json.dumps(command_data, ensure_ascii=False)
            base64_command_data = base64.b64encode(json_string.encode('utf-8')).decode('utf-8')
            bkav_payload = {
                "partnerGUID": conf["partnerGUID"],
                "CommandData": base64_command_data
            }

            api_url = conf["url_api_link_test"] if conf.get("env_testing") == "ON" else conf["url_api_link"]

            headers = {
                "Content-Type": "application/json",
                "User-Agent": "MontechPos/1.0",
                "Accept": "application/json"
            }

            if preview:
                return {
                    "success": True,
                    "invoiceNo": log.transactionCode,
                    "invoiceNumber": log.transactionCode,
                    "invoiceId": log.transactionCode,
                    "message": "Preview mode - Dữ liệu đã được chuẩn bị"
                }

            res = requests.post(api_url, json=bkav_payload, headers=headers, timeout=30)

            invoice_no = None
            is_success = False
            error_message = None

            if res.status_code == 200:
                try:
                    response_data = res.json()
                    if isinstance(response_data, dict) and "d" in response_data:
                        decoded = base64.b64decode(response_data["d"]).decode("utf-8")
                        actual_response = json.loads(decoded)
                        logger.info(f"📥 JSON sau khi decode từ BKAV:\n{json.dumps(actual_response, indent=2, ensure_ascii=False)}")
                    else:
                        actual_response = response_data

                    object_data = actual_response.get("Object", "[]")
                    if isinstance(object_data, str):
                        objects = json.loads(object_data)
                    else:
                        objects = object_data

                    if isinstance(objects, list) and len(objects) > 0:
                        invoice_no = objects[0].get("InvoiceNo")
                        if invoice_no:
                            is_success = True
                        else:
                            error_message = "BKAV không trả về InvoiceNo"
                    else:
                        error_message = "Không tìm thấy danh sách hóa đơn"
                except Exception as e:
                    error_message = f"Lỗi phân tích phản hồi BKAV: {e}"
            else:
                error_message = f"BKAV HTTP {res.status_code}: {res.text}"

            if is_success:
                log.invoiceNumber = invoice_no
                log.verificationCode = invoice_no
                log.isInvoiced = True
                log.provider = "bkav"
                log.invoiceId = invoice_no
                db.session.commit()

                # 🔥 Ghi file log .txt giống VNPT
                try:
                    from datetime import datetime
                    date_folder = datetime.now().strftime('%Y%m%d')
                    debug_dir = os.path.join("debug", date_folder)
                    os.makedirs(debug_dir, exist_ok=True)

                    shdon = str(invoice_no)
                    filename = f"{shdon}_{log.transactionCode}_bkav.txt"
                    full_path = os.path.join(debug_dir, filename)

                    with open(full_path, "w", encoding="utf-8") as f:
                        f.write("PAYLOAD:\n")
                        f.write(json.dumps(command_data, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("BKAV Payload gửi đi (base64):\n")
                        f.write(json.dumps(bkav_payload, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("BKAV Response (decode):\n")
                        f.write(json.dumps(actual_response, indent=2, ensure_ascii=False))
                except Exception as log_ex:
                    logger.warning(f"⚠️ Không thể ghi file log BKAV TXT: {log_ex}")

                with app.app_context():
                    sql = text("""
                        INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                        VALUES (:code, :inv, :data)
                    """)
                    db.session.execute(sql, {
                        "code": log.transactionCode,
                        "inv": invoice_no,
                        "data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                    db.session.commit()

                return {
                    "success": True,
                    "invoiceNo": invoice_no,
                    "invoiceNumber": invoice_no,
                    "invoiceId": invoice_no,
                    "message": "Hóa đơn BKAV đã được tạo thành công"
                }
            else:
                # 🔥 Ghi file lỗi nếu BKAV trả về lỗi
                try:
                    from datetime import datetime
                    date_folder = datetime.now().strftime('%Y%m%d')
                    debug_dir = os.path.join("debug", date_folder)
                    os.makedirs(debug_dir, exist_ok=True)

                    filename = f"{log.transactionCode}_bkav_ERR.txt"
                    full_path = os.path.join(debug_dir, filename)

                    with open(full_path, "w", encoding="utf-8") as f:
                        f.write("PAYLOAD:\n")
                        f.write(json.dumps(command_data, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("BKAV Payload gửi đi (base64):\n")
                        f.write(json.dumps(bkav_payload, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("BKAV Response (decode):\n")
                        f.write(json.dumps(actual_response if 'actual_response' in locals() else {}, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("LỖI:\n" + error_message)
                except Exception as log_ex:
                    logger.warning(f"⚠️ Không thể ghi file log BKAV TXT (err): {log_ex}")

                return {"success": False, "message": f"Lỗi BKAV: {error_message}"}


        except Exception as e:
            return {"success": False, "message": f"Lỗi khi gửi BKAV API: {e}"}


    # === Bắt đầu xử lý chính ===
    if not preview and log.isInvoiced and log.invoiceNumber and log.invoiceNumber.strip():
        return {"success": False, "message": "Giao dịch đã được xuất hóa đơn"}

    transaction_code = log.transactionCode
    if not preview:
        with _bkav_processing_lock:
            if transaction_code in _bkav_processing_transactions:
                return {"success": False, "message": "Giao dịch đang được xử lý"}
            _bkav_processing_transactions.add(transaction_code)

    try:
        today_str = datetime.now().strftime('%Y-%m-%d')
        raw_thue = custom_data.get("thue", 10 if log.fuelType in ["A95", "A92", "DO"] else 8)
        try:
            thue = int(float(raw_thue))
        except:
            thue = 10

        sl = round(log.transactionAmount / 1000, 4)
        dg_chua_vat = log.transactionPrice / (1 + thue / 100)
        tt_chua_vat = log.transactionCost / (1 + thue / 100)
        tong_cong = log.transactionCost
        tien_thue = tong_cong - tt_chua_vat
        cost_text = num2words(int(tong_cong), lang='vi').title() + " đồng"
        thhdv = map_fuel_name(log.fuelType)
        validated = validate_bkav_customer_data(custom_data)
        tax_id, tax_rate = get_bkav_tax_rate_mapping(thue)

        invoice_data = {
            "BuyerName": validated["buyer_name"],
            "BuyerTaxCode": validated["buyer_tax_code"],
            "BuyerUnitName": validated["buyer_unit_name"],
            "BuyerAddress": validated["buyer_address"],
            "BuyerBankAccount": custom_data.get("stk", ""),  # 🔥 thêm dòng này
            "ReceiverEmail": validated["receiver_email"],
            "ReceiverMobile": validated["receiver_mobile"],
            "ReceiverAddress": validated["receiver_address"],
            "ReceiverName": validated["receiver_name"],
            "CCCD": validated["cccd"],
            "FiscalCodes": validated["fiscal_codes"],
            "UserDefine": validated["user_define"],
            "InvoiceTypeID": int(conf.get("invoice_type_id", 1)),
            "InvoiceDate": today_str,
            "PayMethodID": validated["payment_method_id"],
            "ReceiveTypeID": 2,
            "Note": "",
            "BillCode": "",
            "CurrencyID": conf.get("currency_id", "VND"),
            "ExchangeRate": int(conf.get("exchange_rate", 1)),
            "InvoiceForm": "",
            "InvoiceSerial": "",
            "InvoiceNo": 0
        }


        invoice_details = [{
            "ItemName": thhdv,
            "UnitName": "Lít",
            "Qty": sl,
            "Price": round(dg_chua_vat, int(conf.get("no_digit_of_price", 4))),
            "Amount": round(tt_chua_vat),
            "TaxRateID": tax_id,
            "TaxRate": tax_rate,
            "TaxAmount": round(tien_thue),
            "IsDiscount": False
        }]

        command_data = {
            "CmdType": conf.get("cmd_type", "100"),
            "CommandObject": [{
                "Invoice": invoice_data,
                "ListInvoiceDetailsWS": invoice_details,
                "ListInvoiceAttachFileWS": [],
                "PartnerInvoiceID": 0,
                "PartnerInvoiceStringID": transaction_code
            }]
        }

        result = handle_bkav_submit(log, command_data, conf, custom_data=custom_data, preview=preview)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType,
            "nlap": today_str
        })
        return result

    except Exception as e:
        return {"success": False, "message": f"Lỗi khi xử lý BKAV: {e}"}
    finally:
        if not preview:
            with _bkav_processing_lock:
                _bkav_processing_transactions.discard(transaction_code)
                
# Xuất gộp BKAV
def create_invoice_bkav_from_preview(preview_data, custom_data):
    import json, os, time, datetime
    from flask import current_app
    from app import logger, app, db, Log
    from send_invoice import handle_bkav
    from sqlalchemy import text

    logger.info(f"🔥 DEBUG preview_data nhận được (BKAV): {json.dumps(preview_data, ensure_ascii=False)}")

    # Tạo log giả lập từ preview_data
    class PreviewLog:
        def __init__(self, d):
            self.transactionCode = d.get("transactionCode") or "999999_" + str(int(time.time()))
            self.transactionAmount = d.get("transactionAmount")
            self.transactionCost = d.get("transactionCost")
            self.transactionPrice = d.get("transactionPrice")
            self.fuelType = d.get("fuelType")
            self.transactionDate = d.get("transactionDate") or d.get("nl") or datetime.date.today().isoformat()
            self.invoiceNumber = None
            self.invoiceId = None
            self.isInvoiced = False
            self.verificationCode = None
            self.provider = "bkav"

    log = PreviewLog(preview_data)

    # Load cấu hình BKAV
    config_path = os.path.join("config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    conf = config.get("BKAV") or {}

    # Đảm bảo custom_data là dict
    try:
        if isinstance(custom_data, str):
            custom_data = json.loads(custom_data)
        elif not isinstance(custom_data, dict):
            custom_data = {}
    except:
        custom_data = {}

    result = handle_bkav(log, preview=False, custom_data=custom_data, conf=conf)

    # 🔥 Ghi log .txt cho cả thành công và lỗi
    try:
        today = datetime.date.today().strftime("%Y%m%d")
        debug_dir = os.path.join("debug", today)
        os.makedirs(debug_dir, exist_ok=True)

        is_success = result.get("success", False)
        shdon = result.get("invoiceNumber", "CHUA_RO")
        filecode = log.transactionCode
        filename = f"{shdon}_{filecode}_bkav.txt" if is_success else f"{filecode}_bkav_ERR.txt"
        full_path = os.path.join(debug_dir, filename)

        with open(full_path, "w", encoding="utf-8") as f:
            f.write("📤 BKAV HÓA ĐƠN GỘP:\n\n")
            f.write("preview_data:\n")
            f.write(json.dumps(preview_data, indent=2, ensure_ascii=False) + "\n\n")
            f.write("custom_data:\n")
            f.write(json.dumps(custom_data, indent=2, ensure_ascii=False) + "\n\n")
            f.write("result:\n")
            f.write(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.warning(f"⚠️ Không thể ghi file log TXT BKAV gộp: {e}")

    # Nếu lỗi thì dừng tại đây
    if not result.get("success"):
        return result

    # Cập nhật lại các giao dịch gốc đã gộp
    try:
        gop_codes = preview_data.get("gopFromCodes", [])
        shdon = result.get("invoiceNumber")

        if gop_codes and shdon:
            with app.app_context():
                logs = db.session.query(Log).filter(Log.transactionCode.in_(gop_codes)).all()
                for l in logs:
                    l.invoiceNumber = shdon
                    l.verificationCode = shdon
                    l.isInvoiced = True
                    l.provider = "bkav"
                db.session.commit()

                # Lưu vào invoice_log
                for l in logs:
                    sql = text("""
                        INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                        VALUES (:code, :inv, :data)
                        ON DUPLICATE KEY UPDATE
                            invoice_number = VALUES(invoice_number),
                            custom_data = VALUES(custom_data)
                    """)
                    db.session.execute(sql, {
                        "code": l.transactionCode,
                        "inv": shdon,
                        "data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                db.session.commit()

    except Exception as e:
        logger.warning(f"❌ Lỗi khi cập nhật log BKAV gộp: {e}")

    return result

#========== VIETTEL ======================================================================================================================================
# Validation function for Viettel (aligned with BKAV pattern)
def validate_viettel_customer_data(custom_data):
    """Validate and normalize customer data for Viettel API"""
    v = {}
    v["buyer_name"] = custom_data.get("ho_ten", "Người mua không lấy hóa đơn").strip()
    v["buyer_legal_name"] = custom_data.get("ten", "").strip() or v["buyer_name"]
    v["buyer_tax_code"] = custom_data.get("mst", "").strip()
    v["buyer_address"] = custom_data.get("dchi", "").strip()
    v["buyer_email"] = custom_data.get("email", "").strip()
    v["buyer_phone"] = custom_data.get("phone", "").strip()
    v["plate"] = custom_data.get("plate", "").strip()
    v["payment_method"] = custom_data.get("htttoan", "Tiền mặt/Chuyển khoản").strip()

    # CCCD validation (12 digits required)
    cccd = custom_data.get("cccd", "").strip()
    if cccd and cccd.isdigit() and len(cccd) == 12:
        v["buyer_id_type"] = "1"  # 1: Số CMND/CCCD
        v["buyer_id_no"] = cccd
    else:
        v["buyer_id_type"] = ""
        v["buyer_id_no"] = ""

    # Budget code validation (exactly 7 characters required)
    mdvqhns = custom_data.get("mdvqhns", "").strip()
    if mdvqhns and len(mdvqhns) == 7:
        v["buyer_budget_code"] = mdvqhns
    else:
        v["buyer_budget_code"] = ""

    return v

# Hóa đơn điện tử Viettel
def handle_viettel(log, preview, custom_data, conf):
    from datetime import datetime
    import json, os
    from num2words import num2words
    import requests
    from app import db, app
    from sqlalchemy import text
    import threading
    import time
    import uuid

    _viettel_processing_lock = threading.Lock()
    global _viettel_processing_transactions
    if "_viettel_processing_transactions" not in globals():
        _viettel_processing_transactions = set()

    transaction_code = log.transactionCode

    # Kiểm tra duplicate processing
    with _viettel_processing_lock:
        if transaction_code in _viettel_processing_transactions:
            return {"success": False, "message": f"Giao dịch {transaction_code} đang được xử lý bởi thread khác"}
        _viettel_processing_transactions.add(transaction_code)

    try:
        # CHẶN GỬI LẠI nếu log đã có hóa đơn (aligned with BKAV/VNPT pattern)
        if not preview:
            if log.isInvoiced or (log.invoiceNumber and log.invoiceNumber.strip() not in ["", "CHUA_RO_SOHĐ"]):
                logger.info(f"⚠️ [VIETTEL] Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}")
                return {
                    "success": False,
                    "message": f"Giao dịch {log.transactionCode} đã có hóa đơn: {log.invoiceNumber}"
                }

        # ✅ SIMPLIFIED: Only log when not preview mode
        if not preview:
            logger.info(f"🔵 [VIETTEL] Bắt đầu xử lý: {transaction_code}")

        # Xử lý thuế suất (aligned with VNPT pattern)
        raw_thue = str(custom_data.get("thueStr", custom_data.get("thue", "10"))).strip()
        if not preview:
            logger.info(f"🧪 [VIETTEL] Thuế truyền vào: {raw_thue} | fuelType = {log.fuelType}")

        if raw_thue == "KKKNT":
            thue = 0
        elif raw_thue == "KCT":
            thue = 0
        else:
            try:
                thue = float(raw_thue)
            except (TypeError, ValueError):
                thue = 10

        # Tính toán dữ liệu hóa đơn (tái sử dụng logic chung)
        thhdv = map_fuel_name(log.fuelType)
        sl = round(log.transactionAmount / 1000, 4)
        dg_chua_vat = log.transactionPrice / (1 + thue / 100)
        tt_chua_vat = log.transactionCost / (1 + thue / 100)
        tong_cong = log.transactionCost
        tien_thue = tong_cong - tt_chua_vat
        cost_text = num2words(int(tong_cong), lang='vi').title() + " đồng"

        # ✅ FIX: Use validation function like BKAV for consistency
        validated = validate_viettel_customer_data(custom_data)

        # Extract validated data
        ho_ten = validated["buyer_name"]
        ten = validated["buyer_legal_name"]
        mst = validated["buyer_tax_code"]
        dchi = validated["buyer_address"]
        email = validated["buyer_email"]
        phone = validated["buyer_phone"]
        plate = validated["plate"]
        htttoan = validated["payment_method"]
        buyer_budget_code = validated["buyer_budget_code"]
        buyer_id_type = validated["buyer_id_type"]
        buyer_id_no = validated["buyer_id_no"]

        # ✅ SIMPLIFIED: Remove detailed validation logs for cleaner output

        if not preview:
            logger.info(
                f"🧪 [VIETTEL] Thông tin thuế tạo payload: "
                f"Thuế suất = {thue}%, "
                f"Nhiên liệu = {thhdv}, "
                f"Tiền thuế = {tien_thue:.0f}, "
                f"Tổng tiền sau thuế = {tong_cong:.0f}"
            )

        # Tạo timestamp
        current_timestamp = int(time.time() * 1000)

        # Tạo payload Viettel
        viettel_payload = {
            "generalInvoiceInfo": {
                "transactionUuid": transaction_code,
                "invoiceType": conf.get("invoice_type", "1"),
                "templateCode": conf.get("template_code", "1/395"),
                "invoiceSeries": conf.get("invoice_series", "C25MTC"),
                "invoiceIssuedDate": current_timestamp,
                "currencyCode": "VND",
                "adjustmentType": "1",
                "paymentStatus": True,
                "cusGetInvoiceRight": True,
                "reservationCode": "",
                "exchangeRate": int(conf.get("exchange_rate", 1)),
                "validation": int(conf.get("validation", 0))
            },
            "buyerInfo": {
                "buyerName": ho_ten,
                "buyerLegalName": ten or ho_ten,
                "buyerTaxCode": mst,
                "buyerAddressLine": dchi,
                "buyerPhoneNumber": phone,
                "buyerBudgetCode": buyer_budget_code,  # ✅ FIX: Validated 7-character code
                "buyerEmail": email,
                "buyerIdType": buyer_id_type,  # ✅ FIX: Validated CCCD type
                "buyerIdNo": buyer_id_no,      # ✅ FIX: Validated 12-digit CCCD
                "buyerCode": buyer_budget_code,  # ✅ FIX: Use budget code for consistency
                "buyerPostalCode": None,
                "buyerDistrictName": None,
                "buyerCityName": None,
                "buyerCountryCode": None,
                "buyerFaxNumber": None,
                "buyerBankName": "",
                "buyerBankAccount": "",
                "buyerNotGetInvoice": 0
            },
            "sellerInfo": {
                "sellerLegalName": conf.get("seller_name", ""),
                "sellerTaxCode": "",
                "sellerAddressLine": conf.get("seller_address", ""),
                "sellerPhoneNumber": conf.get("seller_phone", "0"),
                "sellerFaxNumber": "",
                "sellerBankName": "",
                "sellerBankAccount": "",
                "sellerCode": "",
                "sellerEmail": "",
                "sellerWebsite": ""
            },
            "extAttribute": [],
            "payments": [
                {
                    "paymentMethodName": htttoan
                }
            ],
            "deliveryInfo": {},
            "itemInfo": [
                {
                    "lineNumber": 1,
                    "selection": 1,
                    "itemCode": log.fuelType,
                    "itemName": thhdv,
                    "unitName": "Lít",
                    "itemNote": "",
                    "unitPrice": round(dg_chua_vat, int(conf.get("no_digit_of_price", 4))),
                    "unitPriceWithTax": None,
                    "quantity": sl,
                    "itemTotalAmountWithoutTax": round(tt_chua_vat),
                    "taxPercentage": thue,
                    "taxAmount": round(tien_thue),
                    "discount": 0.0,
                    "itemDiscount": 0.0,
                    "batchNo": "",
                    "itemTotalAmountWithTax": round(tong_cong),
                    "itemTotalAmountAfterDiscount": round(tt_chua_vat),
                    "expDate": None
                }
            ],
            "discountItemInfo": [],
            "summarizeInfo": {
                "sumOfTotalLineAmountWithoutTax": round(tt_chua_vat),
                "totalAmountWithoutTax": round(tt_chua_vat),
                "totalTaxAmount": round(tien_thue),
                "totalAmountWithTax": round(tong_cong),
                "totalAmountAfterDiscount": round(tt_chua_vat),
                "totalAmountWithTaxInWords": cost_text,
                "discountAmount": 0.0,
                "settlementDiscountAmount": 0.0,
                "taxPercentage": thue
            },
            "taxBreakdowns": [
                {
                    "taxPercentage": thue,
                    "taxableAmount": round(tt_chua_vat),
                    "taxAmount": round(tien_thue)
                }
            ],
            "metadata": [],
            "customFields": [],
            "meterReading": [],
            "fuelReading": [
                {
                    "idLog": 1,
                    "noteLog": thhdv,
                    "pumpCode": transaction_code,
                    "pumpName": str(log.pumpSerial or ""),
                    "qtyLog": sl,
                    "priceLog": log.transactionPrice,
                    "productCode": log.fuelType,
                    "productName": thhdv,
                    "startDate": current_timestamp,
                    "endDate": current_timestamp,
                    "batch": "1",
                    "thanhTienLog": round(tong_cong)
                }
            ]
        }

        # Thêm biển số xe nếu có
        if plate:
            viettel_payload["metadata"].append({
                "keyTag": "numberPlate",
                "keyLabel": "Biển số xe",
                "dateValue": None,
                "stringValue": plate,
                "numberValue": None,
                "valueType": "text",
                "isRequired": False,
                "isSeller": False,
                "required": False
            })

        if preview:
            return {
                "success": True,
                "invoiceNo": transaction_code,
                "invoiceNumber": transaction_code,
                "invoiceId": transaction_code,
                "message": "Preview mode - Dữ liệu đã được chuẩn bị",
                "sl": sl,
                "dgChuaVat": f"{dg_chua_vat:.4f}",
                "ttChuaVat": round(tt_chua_vat),
                "tienThue": round(tien_thue),
                "ttVat": round(tong_cong),
                "chu": cost_text,
                "thhdv": thhdv,
                "fuelType": log.fuelType
            }

        # Gửi request đến Viettel API
        result = handle_viettel_submit(log, viettel_payload, conf, custom_data=custom_data, preview=preview)
        result.update({
            "sl": sl,
            "dgChuaVat": f"{dg_chua_vat:.4f}",
            "ttChuaVat": round(tt_chua_vat),
            "tienThue": round(tien_thue),
            "ttVat": round(tong_cong),
            "chu": cost_text,
            "thhdv": thhdv,
            "fuelType": log.fuelType
        })
        return result

    except Exception as e:
        logger.error(f"❌ [VIETTEL] Lỗi: {str(e)}")
        return {"success": False, "message": f"Lỗi khi gửi hóa đơn VIETTEL: {str(e)}"}
    finally:
        # Cleanup
        with _viettel_processing_lock:
            _viettel_processing_transactions.discard(transaction_code)

def handle_viettel_submit(log, payload, conf, custom_data=None, preview=False):
    from app import db, app
    from sqlalchemy import text
    import requests
    import json
    from datetime import datetime

    try:
        # Lấy thông tin API
        api_url = conf.get("url_api_link")
        username = conf.get("username")
        password = conf.get("password")
        tax_code = conf.get("tax_code")

        if not all([api_url, username, password, tax_code]):
            logger.error("❌ [VIETTEL] Thiếu thông tin cấu hình API")
            return {"success": False, "message": "Thiếu thông tin cấu hình Viettel API"}

        # Tạo URL đầy đủ
        full_api_url = f"{api_url}/{tax_code}"

        # Headers cho request (aligned with BKAV pattern)
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "MontechPos/1.0",
            "Accept": "application/json"
        }

        # Thêm authentication nếu cần
        if username and password:
            import base64
            credentials = base64.b64encode(f"{username}:{password}".encode()).decode()
            headers["Authorization"] = f"Basic {credentials}"

        if not preview:
            logger.info(f"🔵 [VIETTEL] Gửi request đến: {full_api_url}")
        # ✅ SIMPLIFIED: Remove detailed payload log for cleaner output

        # Gửi request với error handling tương tự BKAV/VNPT
        try:
            response = requests.post(full_api_url, json=payload, headers=headers, timeout=30)
        except requests.exceptions.Timeout:
            logger.error("❌ [VIETTEL] Timeout khi gọi API")
            return {"success": False, "message": "Timeout khi gọi Viettel API"}
        except requests.exceptions.ConnectionError:
            logger.error("❌ [VIETTEL] Lỗi kết nối đến API")
            return {"success": False, "message": "Không thể kết nối đến Viettel API"}
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ [VIETTEL] Lỗi request: {str(e)}")
            return {"success": False, "message": f"Lỗi khi gọi Viettel API: {str(e)}"}

        if not preview:
            logger.info(f"🔵 [VIETTEL] Response status: {response.status_code}")
            logger.info(f"🔵 [VIETTEL] Response: {response.text}")

        # Xử lý response status (aligned with BKAV/VNPT pattern)
        if response.status_code != 200:
            error_msg = f"Viettel API lỗi: {response.status_code} - {response.text}"
            logger.error(f"❌ [VIETTEL] {error_msg}")
            return {"success": False, "message": error_msg, "response": response.text}

        # Parse response với error handling
        try:
            response_data = response.json()
        except json.JSONDecodeError as e:
            error_msg = "Viettel API trả về dữ liệu không hợp lệ"
            logger.error(f"❌ [VIETTEL] {error_msg}: {str(e)}")
            return {"success": False, "message": error_msg, "response": response.text}

        # Xử lý response (aligned with BKAV/VNPT success patterns)
        if response_data.get("errorCode") is None and response_data.get("result"):
            result_data = response_data.get("result", {})
            invoice_no = result_data.get("invoiceNo")
            reservation_code = result_data.get("reservationCode", "")

            if invoice_no:
                # Lưu file debug (consistent với BKAV/VNPT pattern)
                try:
                    date_folder = datetime.now().strftime('%Y%m%d')
                    debug_dir = os.path.join("debug", date_folder)
                    os.makedirs(debug_dir, exist_ok=True)

                    filename = f"{invoice_no}_{log.transactionCode}_viettel.txt"
                    full_path = os.path.join(debug_dir, filename)

                    with open(full_path, "w", encoding="utf-8") as f:
                        f.write("PAYLOAD:\n")
                        f.write(json.dumps(payload, indent=2, ensure_ascii=False) + "\n\n")
                        f.write("VIETTEL Response:\n")
                        f.write(json.dumps(response_data, indent=2, ensure_ascii=False))
                except Exception as debug_error:
                    logger.warning(f"⚠️ [VIETTEL] Không thể ghi debug file: {str(debug_error)}")
                    filename = "debug_error"

                # ✅ FIX: Database update moved to app.py level like BKAV/VNPT for consistency
                # Only save to invoice_log table here for debugging purposes
                try:
                    with app.app_context():
                        sql = text("""
                            INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                            VALUES (:code, :inv, :data)
                            ON DUPLICATE KEY UPDATE
                                invoice_number = VALUES(invoice_number),
                                custom_data = VALUES(custom_data)
                        """)
                        db.session.execute(sql, {
                            "code": log.transactionCode,
                            "inv": invoice_no,
                            "data": json.dumps(custom_data or {}, ensure_ascii=False)
                        })
                        db.session.commit()
                except Exception as e:
                    logger.warning(f"⚠️ [VIETTEL] Không thể ghi invoice_log: {str(e)}")

                if not preview:
                    logger.info(f"✅ [VIETTEL] Xuất hóa đơn thành công: {invoice_no}")

                # ✅ FIX: Update database in handle_viettel for auto processing consistency
                try:
                    log.verificationCode = str(invoice_no)
                    log.invoiceNumber = str(invoice_no)
                    log.isInvoiced = True
                    log.provider = "viettel"
                    log.invoiceId = str(invoice_no)
                    db.session.commit()
                    if not preview:
                        logger.info(f"🔵 [VIETTEL] Database updated: {invoice_no}")
                except Exception as db_error:
                    logger.error(f"❌ [VIETTEL] Database update failed: {str(db_error)}")
                    # Don't fail the whole operation if DB update fails
                    pass

                return {
                    "success": True,
                    "invoiceNo": invoice_no,
                    "invoiceNumber": invoice_no,
                    "invoiceId": invoice_no,
                    "reservationCode": reservation_code,
                    "message": f"Xuất hóa đơn thành công. Số HĐ: {invoice_no}",
                    "response": response.text,  # Include response for consistency
                    "debug_file": filename
                }
            else:
                error_msg = "Viettel không trả về số hóa đơn"
                logger.error(f"❌ [VIETTEL] {error_msg}")
                return {"success": False, "message": error_msg, "response": response.text}
        else:
            # Xử lý lỗi từ API (aligned với VNPT error handling pattern)
            error_code = response_data.get("errorCode", "Unknown")
            error_message = response_data.get("description", "Lỗi không xác định")
            full_error_msg = f"Viettel lỗi: {error_code} - {error_message}"

            logger.error(f"❌ [VIETTEL] {full_error_msg}")

            # ✅ FIX: Ghi log lỗi vào file như BKAV
            try:
                date_folder = datetime.now().strftime('%Y%m%d')
                debug_dir = os.path.join("debug", date_folder)
                os.makedirs(debug_dir, exist_ok=True)

                timestamp = datetime.now().strftime('%H%M%S')
                filename = f"ERROR_{log.transactionCode}_{timestamp}_viettel.txt"
                full_path = os.path.join(debug_dir, filename)

                with open(full_path, "w", encoding="utf-8") as f:
                    f.write("VIETTEL PAYLOAD GỬI ĐI:\n")
                    f.write(json.dumps(payload, indent=2, ensure_ascii=False) + "\n\n")
                    f.write("VIETTEL RESPONSE:\n")
                    f.write(response.text + "\n\n")
                    f.write("LỖI:\n" + full_error_msg)
            except Exception as log_ex:
                logger.warning(f"⚠️ [VIETTEL] Không thể ghi file log lỗi: {log_ex}")

            return {
                "success": False,
                "message": full_error_msg,
                "response": response.text,
                "errorCode": error_code
            }

    except requests.exceptions.Timeout:
        error_msg = "Timeout khi gọi Viettel API"
        logger.error(f"❌ [VIETTEL] {error_msg}")
        return {"success": False, "message": error_msg}
    except requests.exceptions.RequestException as e:
        error_msg = f"Lỗi kết nối Viettel API: {str(e)}"
        logger.error(f"❌ [VIETTEL] {error_msg}")
        return {"success": False, "message": error_msg}
    except Exception as e:
        error_msg = f"Lỗi không mong đợi trong handle_viettel_submit: {str(e)}"
        logger.error(f"❌ [VIETTEL] {error_msg}")
        return {"success": False, "message": f"Lỗi hệ thống: {str(e)}"}

# Xuất gộp cho viettel (aligned with VNPT/MOBIFONE/BKAV pattern)
def create_invoice_viettel_from_preview(preview_data, custom_data):
    import json, os, time, datetime
    from flask import current_app
    from app import logger, app, db, Log
    from send_invoice import handle_viettel
    from sqlalchemy import text

    logger.info(f"🔥 [VIETTEL] DEBUG preview_data nhận được: {json.dumps(preview_data, ensure_ascii=False)}")

    # Load config
    config_path = os.path.join("config", "config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    conf = config.get("VIETTEL", {})

    if not conf:
        return {"success": False, "message": "Không tìm thấy cấu hình VIETTEL"}

    # Tạo log ảo từ preview_data (aligned with VNPT/BKAV/MOBIFONE pattern)
    class MockLog:
        def __init__(self, data):
            self.transactionCode = data.get("transactionCode", f"999999_{int(time.time())}")
            self.fuelType = data.get("fuelType", "A95")
            # ✅ FIX: Use correct mapping like VNPT/BKAV/MOBIFONE
            self.transactionAmount = data.get("transactionAmount")
            self.transactionCost = data.get("transactionCost")
            self.transactionPrice = data.get("transactionPrice")
            self.transactionDate = data.get("transactionDate") or data.get("nl") or datetime.datetime.now().isoformat()
            self.isInvoiced = False
            self.invoiceNumber = None
            self.invoiceId = None
            self.verificationCode = None
            self.provider = "viettel"
            # ✅ FIX: Add missing pumpSerial attribute
            self.pumpSerial = data.get("pumpSerial", "")

    log = MockLog(preview_data)

    # Gọi handle_viettel để xuất hóa đơn
    result = handle_viettel(log, preview=False, custom_data=custom_data, conf=conf)

    # Nếu lỗi thì return luôn
    if not result.get("success"):
        logger.warning("❌ [VIETTEL] Gửi hóa đơn gộp thất bại")
        return result

    # Gán lại số HĐ cho các log thật đã gộp (aligned with VNPT/MOBIFONE pattern)
    try:
        gop_codes = preview_data.get("gopFromCodes", [])
        shdon = result.get("invoiceNumber") or result.get("invoiceNo")

        logger.info(f"🧾 [VIETTEL] Danh sách gopFromCodes nhận được: {gop_codes}")
        logger.info(f"✅ [VIETTEL] Số hóa đơn cần gán: {shdon}")

        if shdon and gop_codes:
            with app.app_context():
                logs = db.session.query(Log).filter(Log.transactionCode.in_(gop_codes)).all()
                logger.info(f"🔍 [VIETTEL] Tìm thấy {len(logs)} log khớp trong DB để gán số hóa đơn.")
                for l in logs:
                    logger.info(f"✅ [VIETTEL] Gán số HĐ {shdon} cho log: {l.transactionCode}")
                    l.invoiceNumber = shdon
                    l.verificationCode = shdon
                    l.isInvoiced = True
                    l.provider = "viettel"
                db.session.commit()

                # Lưu vào invoice_log (aligned with BKAV pattern)
                for l in logs:
                    sql = text("""
                        INSERT INTO invoice_log (transaction_code, invoice_number, custom_data)
                        VALUES (:code, :inv, :data)
                        ON DUPLICATE KEY UPDATE
                            invoice_number = VALUES(invoice_number),
                            custom_data = VALUES(custom_data)
                    """)
                    db.session.execute(sql, {
                        "code": l.transactionCode,
                        "inv": shdon,
                        "data": json.dumps(custom_data or {}, ensure_ascii=False)
                    })
                db.session.commit()

    except Exception as e:
        logger.warning(f"❌ [VIETTEL] Lỗi khi cập nhật log gộp: {e}")

    return result