<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug API Credentials</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input { width: 300px; padding: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug API Credentials</h1>
        
        <div class="section">
            <h3>1. <PERSON><PERSON>m tra Session hiện tại</h3>
            <button class="button" onclick="checkSession()">Kiểm tra Session</button>
            <div id="sessionResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. Kiểm tra User trong Database</h3>
            <div class="form-group">
                <label>Username:</label>
                <input type="text" id="username" value="ketoan" placeholder="Nhập username">
            </div>
            <button class="button" onclick="checkUser()">Kiểm tra User</button>
            <div id="userResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>3. Refresh API Credentials</h3>
            <button class="button" onclick="refreshCredentials()">Refresh Session</button>
            <div id="refreshResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>4. Cập nhật API Credentials</h3>
            <div class="form-group">
                <label>Username:</label>
                <input type="text" id="updateUsername" value="ketoan">
            </div>
            <div class="form-group">
                <label>API Account:</label>
                <input type="text" id="apiAccount" value="montech1">
            </div>
            <div class="form-group">
                <label>API Pass:</label>
                <input type="text" id="apiPass" value="Einv@oi@vn#pt25">
            </div>
            <button class="button" onclick="updateCredentials()">Cập nhật</button>
            <div id="updateResult" class="result"></div>
        </div>
    </div>

    <script>
        async function checkSession() {
            try {
                const response = await fetch('/api/debug_session');
                const data = await response.json();
                document.getElementById('sessionResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('sessionResult').textContent = 'Lỗi: ' + error.message;
            }
        }
        
        async function checkUser() {
            const username = document.getElementById('username').value;
            try {
                const response = await fetch(`/api/debug_user/${username}`);
                const data = await response.json();
                document.getElementById('userResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('userResult').textContent = 'Lỗi: ' + error.message;
            }
        }
        
        async function refreshCredentials() {
            try {
                const response = await fetch('/api/refresh_api_credentials', { method: 'POST' });
                const data = await response.json();
                document.getElementById('refreshResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('refreshResult').textContent = 'Lỗi: ' + error.message;
            }
        }
        
        async function updateCredentials() {
            const formData = new FormData();
            formData.append('username', document.getElementById('updateUsername').value);
            formData.append('api_account', document.getElementById('apiAccount').value);
            formData.append('api_pass', document.getElementById('apiPass').value);
            
            try {
                const response = await fetch('/update_api_credentials', { 
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                document.getElementById('updateResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('updateResult').textContent = 'Lỗi: ' + error.message;
            }
        }
    </script>
</body>
</html>
