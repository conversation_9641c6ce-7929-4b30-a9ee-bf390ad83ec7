#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để test thông tin user credentials
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, User
    
    print("🔍 Kiểm tra thông tin user credentials...")
    
    with app.app_context():
        # Lấy tất cả users
        users = User.query.all()
        print(f"\n📋 Tổng số users: {len(users)}")
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   Role: {user.role}")
            print(f"   API Account: {user.api_account}")
            print(f"   API Pass: {user.api_pass}")
            print(f"   Has API credentials: {bool(user.api_account and user.api_pass)}")
        
        # Kiểm tra user ketoan cụ thể
        ketoan_user = User.query.filter_by(username='ketoan').first()
        if ketoan_user:
            print(f"\n✅ User 'ketoan' tìm thấy:")
            print(f"   ID: {ketoan_user.id}")
            print(f"   Username: {ketoan_user.username}")
            print(f"   Role: {ketoan_user.role}")
            print(f"   API Account: {ketoan_user.api_account}")
            print(f"   API Pass: {ketoan_user.api_pass}")
        else:
            print(f"\n❌ User 'ketoan' không tìm thấy")

except Exception as e:
    print(f"❌ Lỗi: {e}")
    import traceback
    traceback.print_exc()
